// Test script to verify GitHub token and find repositories
const fs = require('fs');

// Read .env.local file manually
let GITHUB_TOKEN = '';
try {
  const envContent = fs.readFileSync('.env.local', 'utf8');
  const tokenMatch = envContent.match(/GITHUB_TOKEN=(.+)/);
  if (tokenMatch) {
    GITHUB_TOKEN = tokenMatch[1].trim();
  }
} catch (error) {
  console.error('❌ Could not read .env.local file');
}

if (!GITHUB_TOKEN) {
  console.error('❌ GITHUB_TOKEN not found in .env.local');
  process.exit(1);
}

async function testGitHubAccess() {
  try {
    console.log('🔍 Testing GitHub token...');
    
    // Test 1: Check if token is valid
    const userResponse = await fetch('https://api.github.com/user', {
      headers: {
        'Authorization': `Bearer ${GITHUB_TOKEN}`,
        'Accept': 'application/vnd.github.v3+json',
      }
    });
    
    if (!userResponse.ok) {
      console.error('❌ GitHub token is invalid or expired');
      console.error(`Status: ${userResponse.status} ${userResponse.statusText}`);
      return;
    }
    
    const user = await userResponse.json();
    console.log(`✅ Token is valid! Authenticated as: ${user.login}`);
    console.log(`   Account type: ${user.type}`);
    
    // Test 2: List user's repositories
    console.log('\n🔍 Fetching your repositories...');
    const reposResponse = await fetch('https://api.github.com/user/repos?per_page=10', {
      headers: {
        'Authorization': `Bearer ${GITHUB_TOKEN}`,
        'Accept': 'application/vnd.github.v3+json',
      }
    });
    
    if (reposResponse.ok) {
      const repos = await reposResponse.json();
      console.log(`\n📁 Found ${repos.length} repositories (showing first 10):`);
      repos.forEach(repo => {
        console.log(`   - ${repo.full_name} (${repo.private ? 'private' : 'public'})`);
      });
    }
    
    // Test 3: Check Flying-Tea-Squad organization repositories
    console.log('\n🔍 Fetching Flying-Tea-Squad organization repositories...');
    const orgReposResponse = await fetch('https://api.github.com/orgs/Flying-Tea-Squad/repos', {
      headers: {
        'Authorization': `Bearer ${GITHUB_TOKEN}`,
        'Accept': 'application/vnd.github.v3+json',
      }
    });

    if (orgReposResponse.ok) {
      const orgRepos = await orgReposResponse.json();
      console.log(`\n📁 Found ${orgRepos.length} repositories in Flying-Tea-Squad:`);
      orgRepos.forEach(repo => {
        console.log(`   - ${repo.name} (${repo.private ? 'private' : 'public'})`);
        console.log(`     Full path: ${repo.full_name}`);
        console.log(`     Description: ${repo.description || 'No description'}`);
        console.log('');
      });

      if (orgRepos.length === 0) {
        console.log('   No repositories found or no access to organization repositories');
      }
    } else {
      console.log(`❌ Could not access Flying-Tea-Squad organization: ${orgReposResponse.status} ${orgReposResponse.statusText}`);
      console.log('   This might mean:');
      console.log('   - The organization name is incorrect');
      console.log('   - Your token does not have access to this organization');
      console.log('   - The organization is private and you are not a member');
    }
    
  } catch (error) {
    console.error('❌ Error testing GitHub access:', error.message);
  }
}

testGitHubAccess();
