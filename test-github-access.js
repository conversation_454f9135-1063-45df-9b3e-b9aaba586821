// Test script to verify GitHub token and find repositories
const fs = require('fs');

// Read .env.local file manually
let GITHUB_TOKEN = '';
try {
  const envContent = fs.readFileSync('.env.local', 'utf8');
  const tokenMatch = envContent.match(/GITHUB_TOKEN=(.+)/);
  if (tokenMatch) {
    GITHUB_TOKEN = tokenMatch[1].trim();
  }
} catch (error) {
  console.error('❌ Could not read .env.local file');
}

if (!GITHUB_TOKEN) {
  console.error('❌ GITHUB_TOKEN not found in .env.local');
  process.exit(1);
}

async function testGitHubAccess() {
  try {
    console.log('🔍 Testing GitHub token...');
    
    // Test 1: Check if token is valid
    const userResponse = await fetch('https://api.github.com/user', {
      headers: {
        'Authorization': `Bearer ${GITHUB_TOKEN}`,
        'Accept': 'application/vnd.github.v3+json',
      }
    });
    
    if (!userResponse.ok) {
      console.error('❌ GitHub token is invalid or expired');
      console.error(`Status: ${userResponse.status} ${userResponse.statusText}`);
      return;
    }
    
    const user = await userResponse.json();
    console.log(`✅ Token is valid! Authenticated as: ${user.login}`);
    console.log(`   Account type: ${user.type}`);
    
    // Test 2: List user's repositories
    console.log('\n🔍 Fetching your repositories...');
    const reposResponse = await fetch('https://api.github.com/user/repos?per_page=10', {
      headers: {
        'Authorization': `Bearer ${GITHUB_TOKEN}`,
        'Accept': 'application/vnd.github.v3+json',
      }
    });
    
    if (reposResponse.ok) {
      const repos = await reposResponse.json();
      console.log(`\n📁 Found ${repos.length} repositories (showing first 10):`);
      repos.forEach(repo => {
        console.log(`   - ${repo.full_name} (${repo.private ? 'private' : 'public'})`);
      });
    }
    
    // Test 3: Check if specific repository exists
    console.log('\n🔍 Testing access to 01talentke_website/01talentke_website...');
    const testRepoResponse = await fetch('https://api.github.com/repos/01talentke_website/01talentke_website', {
      headers: {
        'Authorization': `Bearer ${GITHUB_TOKEN}`,
        'Accept': 'application/vnd.github.v3+json',
      }
    });
    
    if (testRepoResponse.ok) {
      const repo = await testRepoResponse.json();
      console.log(`✅ Repository found: ${repo.full_name}`);
      console.log(`   Description: ${repo.description || 'No description'}`);
      console.log(`   Private: ${repo.private}`);
    } else {
      console.log(`❌ Repository not found or no access: ${testRepoResponse.status} ${testRepoResponse.statusText}`);
      
      // Test alternative paths
      console.log('\n🔍 Trying alternative repository paths...');
      const alternatives = [
        `${user.login}/01talentke_website`,
        `${user.login}/01talentke-website`,
        `01talentke/website`,
        `01talent/website`
      ];
      
      for (const altPath of alternatives) {
        const altResponse = await fetch(`https://api.github.com/repos/${altPath}`, {
          headers: {
            'Authorization': `Bearer ${GITHUB_TOKEN}`,
            'Accept': 'application/vnd.github.v3+json',
          }
        });
        
        if (altResponse.ok) {
          const altRepo = await altResponse.json();
          console.log(`✅ Found alternative: ${altRepo.full_name}`);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Error testing GitHub access:', error.message);
  }
}

testGitHubAccess();
