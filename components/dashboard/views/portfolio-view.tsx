import { use<PERSON>emo } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Github, Target, TrendingUp, CheckCircle, RefreshCw, Eye, Filter, Activity } from "lucide-react"
import { MetricCard } from "@/components/dashboard/cards/metric-card"
import { IssueMetric } from "@/components/dashboard/cards/issue-metric"
import { VelocityChart } from "@/components/dashboard/charts/velocity-chart"
import { ProjectRow } from "@/components/dashboard/shared/project-row"
import { RealTeamMember } from "@/components/dashboard/shared/team-members"
import { AlertItem } from "@/components/dashboard/shared/alert-item"
import { ActivityItem } from "@/components/dashboard/shared/activity-item"
import { MilestoneProgressCard } from "@/components/dashboard/cards/milestone-progress-card"

interface PortfolioViewProps {
  portfolioHealth: number
  milestoneProgress: number
  idealMilestoneProgress: number
  teamVelocity: number
  issueResolution: number
  projectsData: any[]
  refetch: () => void
  selectedMilestone: string
  setSelectedMilestone: (milestone: string) => void
  enhancedTeamData: any
  isLoadingTeam: boolean
}

export function PortfolioView({
  portfolioHealth,
  milestoneProgress,
  idealMilestoneProgress,
  teamVelocity,
  issueResolution,
  projectsData,
  refetch,
  selectedMilestone,
  setSelectedMilestone,
  enhancedTeamData,
  isLoadingTeam,
}: PortfolioViewProps) {
  // Get unique milestones from all projects
  const allMilestones = useMemo(() => {
    const milestones = new Set<string>()
    projectsData.forEach((project) => {
      project.issues.forEach((issue: any) => {
        if (issue.milestone?.title) {
          milestones.add(issue.milestone.title)
        }
      })
    })
    return Array.from(milestones).sort()
  }, [projectsData])

  // Filter projects based on selected milestone
  const filteredProjects = useMemo(() => {
    if (selectedMilestone === "all") return projectsData
    return projectsData.map((project) => ({
      ...project,
      issues: project.issues.filter((issue: any) => issue.milestone?.title === selectedMilestone),
      pullRequests: project.pullRequests.filter((pr: any) => pr.milestone?.title === selectedMilestone),
    }))
  }, [projectsData, selectedMilestone])

  // Calculate filtered milestone progress// Calculate filtered milestone progress
  const filteredMilestoneProgress = useMemo(() => {
    if (selectedMilestone === "all") {
      // Calculate progress for each of the 6 milestones
      const TOTAL_MILESTONES = 6
      let totalWeightedProgress = 0

      for (let milestoneNum = 1; milestoneNum <= TOTAL_MILESTONES; milestoneNum++) {
        const milestoneName = `Milestone ${milestoneNum}`

        // Calculate progress for this specific milestone across all projects
        const projectProgressPercentages = filteredProjects.map(project => {
          const milestoneIssues = project.issues.filter((issue: any) =>
            issue.milestone?.title === milestoneName
          )
          const totalIssues = milestoneIssues.length
          const closedIssues = milestoneIssues.filter((issue: any) => issue.state === "closed").length

          return totalIssues > 0 ? (closedIssues / totalIssues) * 100 : 0
        })

        // Average progress for this milestone across all projects
        const milestoneProgress = projectProgressPercentages.length > 0
          ? projectProgressPercentages.reduce((sum, progress) => sum + progress, 0) / projectProgressPercentages.length
          : 0

        // Each milestone contributes 1/6 to total progress
        totalWeightedProgress += milestoneProgress / TOTAL_MILESTONES
      }

      return Math.round(totalWeightedProgress)
    }

    // Calculate individual project progress percentages for specific milestone
    const projectProgressPercentages = filteredProjects.map(project => {
      const milestoneIssues = project.issues.filter((issue: any) =>
        issue.milestone?.title === selectedMilestone
      )
      const totalIssues = milestoneIssues.length
      const closedIssues = milestoneIssues.filter((issue: any) => issue.state === "closed").length

      return totalIssues > 0 ? (closedIssues / totalIssues) * 100 : 0
    })

    // Return average of all project percentages
    const averageProgress = projectProgressPercentages.length > 0
      ? projectProgressPercentages.reduce((sum, progress) => sum + progress, 0) / projectProgressPercentages.length
      : 0

    return Math.round(averageProgress)
  }, [filteredProjects, selectedMilestone, milestoneProgress])

  // Add this helper function inside PortfolioView:
  const calculateFilteredProgress = (project: any) => {
    if (selectedMilestone !== "all") {
      // Filter issues by selected milestone
      const milestoneIssues = project.issues.filter((issue: any) =>
        issue.milestone?.title === selectedMilestone
      )
      const totalIssues = milestoneIssues.length
      const closedIssues = milestoneIssues.filter((i: any) => i.state === "closed").length
      return totalIssues > 0 ? Math.round((closedIssues / totalIssues) * 100) : 0
    }
    // For "all milestones": use same logic as milestone progress calculation
    const TOTAL_PLANNED_MILESTONES = 6
    const issuesByMilestone = new Map<number, any[]>()

    // Group issues by milestone number
    project.issues.forEach((issue: any) => {
      if (issue.milestone?.title) {
        const milestoneNum = parseInt(issue.milestone.title.match(/Milestone\s+(\d+)/i)?.[1] || '0')
        if (milestoneNum >= 1 && milestoneNum <= 6) {
          if (!issuesByMilestone.has(milestoneNum)) {
            issuesByMilestone.set(milestoneNum, [])
          }
          issuesByMilestone.get(milestoneNum)!.push(issue)
        }
      }
    })

    let totalProgress = 0
    // Calculate progress for each of the 6 planned milestones
    for (let milestoneNum = 1; milestoneNum <= TOTAL_PLANNED_MILESTONES; milestoneNum++) {
      const milestoneIssues = issuesByMilestone.get(milestoneNum) || []
      if (milestoneIssues.length > 0) {
        const closedIssues = milestoneIssues.filter((issue: any) => issue.state === "closed").length
        const milestoneCompletion = (closedIssues / milestoneIssues.length) * 100
        totalProgress += milestoneCompletion
      }
    }

    return Math.round(totalProgress / TOTAL_PLANNED_MILESTONES)
  }

  // Get team count from actual contributor data:
  const getProjectTeamCount = (project: any) => {
    return project.contributors?.length || 2 // fallback to 2 if no data
  }
    // Add this function inside PortfolioView component:
  const calculateProjectStatus = (project: any, progress: number) => {
    if (selectedMilestone === "all") {
      // For "All Milestones": calculate status relative to milestone target
      const milestoneTarget = idealMilestoneProgress // 33% in your case
      const onTrackThreshold = milestoneTarget * 0.75  // 75% of target (25%)
      const atRiskThreshold = milestoneTarget * 0.5    // 50% of target (17%)

      if (progress >= onTrackThreshold) return "on-track"
      if (progress >= atRiskThreshold) return "at-risk"
      return "behind"
    }

    // For individual milestones: 100% is the target
    if (progress >= 75) return "on-track"  // 80%+ of milestone complete
    if (progress >= 50) return "at-risk"   // 50-79% complete
    return "behind"  // <50% complete
  }

  const calculateRiskAlerts = useMemo(() => {
    const risks: Array<{
      title: string
      description: string
      type: "warning" | "error" | "info"
      project: string
      timestamp: Date
      severity: number // 1-5, 5 being most critical
    }> = []

    // Store milestone data for consolidation
    const milestoneData: { [key: string]: {
      title: string
      daysUntilDue: number
      projects: Array<{ name: string, progress: number }>
      timestamp: Date
      isOverdue: boolean
    }} = {}

    projectsData.forEach((project) => {
      const projectName = project.name.charAt(0).toUpperCase() + project.name.slice(1)

      // 1. MILESTONE DELAY RISK - Collect data from all projects
      const currentMilestones = project.milestones || []
      const activeMilestones = currentMilestones.filter((m: any) => m && m.state === "open")

      activeMilestones.forEach((milestone: any) => {
        if (milestone.due_on) {
          const dueDate = new Date(milestone.due_on)
          const now = new Date()
          const daysUntilDue = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

          // Get milestone issues
          const milestoneIssues = project.issues.filter((issue: any) =>
            issue.milestone?.id === milestone.id
          )
          const totalIssues = milestoneIssues.length
          const closedIssues = milestoneIssues.filter((issue: any) => issue.state === "closed").length
          const progress = totalIssues > 0 ? Math.round((closedIssues / totalIssues) * 100) : 0

          // Store milestone data for consolidation
          if (!milestoneData[milestone.title]) {
            milestoneData[milestone.title] = {
              title: milestone.title,
              daysUntilDue,
              projects: [],
              timestamp: new Date(milestone.updated_at),
              isOverdue: daysUntilDue < 0
            }
          }

          milestoneData[milestone.title].projects.push({
            name: projectName,
            progress
          })
        }
      })

      // 2. DEPENDENCY & BLOCKER RISKS
      const blockedIssues = project.issues.filter((issue: any) =>
        issue.labels.some((label: any) =>
          label.name.toLowerCase().includes("blocked") ||
          label.name.toLowerCase().includes("dependency")
        )
      )

      if (blockedIssues.length > 0) {
        const oldestBlocked = blockedIssues.reduce((oldest: any, current: any) =>
          new Date(current.created_at) < new Date(oldest.created_at) ? current : oldest
        )
        const daysBlocked = Math.ceil((Date.now() - new Date(oldestBlocked.created_at).getTime()) / (1000 * 60 * 60 * 24))

        risks.push({
          title: `Dependency Blocker - ${projectName}`,
          description: `${blockedIssues.length} blocked issues, oldest blocked for ${daysBlocked} days`,
          type: daysBlocked > 5 ? "error" : "warning",
          project: projectName,
          timestamp: new Date(oldestBlocked.updated_at),
          severity: daysBlocked > 7 ? 4 : 3
        })
      }

      // 3. STALE PR RISK
      const stalePRs = project.pullRequests.filter((pr: any) => {
        if (pr.state !== "open") return false
        const daysSinceUpdate = (Date.now() - new Date(pr.updated_at).getTime()) / (1000 * 60 * 60 * 24)
        return daysSinceUpdate > 7
      })

      if (stalePRs.length > 2) {
        const oldestPR = stalePRs.reduce((oldest: any, current: any) =>
          new Date(current.updated_at) < new Date(oldest.updated_at) ? current : oldest
        )
        const daysSinceUpdate = Math.ceil((Date.now() - new Date(oldestPR.updated_at).getTime()) / (1000 * 60 * 60 * 24))

        risks.push({
          title: `Stale Code Reviews - ${projectName}`,
          description: `${stalePRs.length} PRs stale for 7+ days, oldest not updated for ${daysSinceUpdate} days`,
          type: "warning",
          project: projectName,
          timestamp: new Date(oldestPR.updated_at),
          severity: 3
        })
      }

      // 4. HIGH ISSUE VELOCITY RISK
      const recentIssues = project.issues.filter((issue: any) => {
        const daysSinceCreated = (Date.now() - new Date(issue.created_at).getTime()) / (1000 * 60 * 60 * 24)
        return daysSinceCreated <= 7
      })

      if (recentIssues.length > 10) {
        risks.push({
          title: `High Issue Influx - ${projectName}`,
          description: `${recentIssues.length} new issues created in last 7 days, potential scope creep`,
          type: "warning",
          project: projectName,
          timestamp: new Date(Math.max(...recentIssues.map((i: any) => new Date(i.created_at).getTime()))),
          severity: 3
        })
      }

      // 5. CRITICAL ISSUE RISK
      const criticalIssues = project.issues.filter((issue: any) =>
        issue.state === "open" && issue.labels.some((label: any) =>
          label.name.toLowerCase().includes("critical") ||
          label.name.toLowerCase().includes("urgent") ||
          label.name.toLowerCase().includes("high")
        )
      )

      if (criticalIssues.length > 0) {
        const oldestCritical = criticalIssues.reduce((oldest: any, current: any) =>
          new Date(current.created_at) < new Date(oldest.created_at) ? current : oldest
        )
        const daysOld = Math.ceil((Date.now() - new Date(oldestCritical.created_at).getTime()) / (1000 * 60 * 60 * 24))

        risks.push({
          title: `Critical Issues - ${projectName}`,
          description: `${criticalIssues.length} critical issues open, oldest for ${daysOld} days`,
          type: "error",
          project: projectName,
          timestamp: new Date(oldestCritical.updated_at),
          severity: 4
        })
      }

      // 6. WORKFLOW FAILURE RISK
      const failedRuns = project.workflowRuns?.filter((run: any) =>
        run.conclusion === "failure" && run.status === "completed"
      ) || []

      const recentFailures = failedRuns.filter((run: any) => {
        const daysSinceRun = (Date.now() - new Date(run.updated_at).getTime()) / (1000 * 60 * 60 * 24)
        return daysSinceRun <= 7
      })

      if (recentFailures.length > 3) {
        risks.push({
          title: `CI/CD Instability - ${projectName}`,
          description: `${recentFailures.length} workflow failures in last 7 days, deployment risk`,
          type: "warning",
          project: projectName,
          timestamp: new Date(Math.max(...recentFailures.map((r: any) => new Date(r.updated_at).getTime()))),
          severity: 3
        })
      }

      // 7. NO RECENT ACTIVITY RISK
      const latestActivity = Math.max(
        ...[
          ...project.commits.map((c: any) => new Date(c.commit.author.date).getTime()),
          ...project.issues.map((i: any) => new Date(i.updated_at).getTime()),
          ...project.pullRequests.map((pr: any) => new Date(pr.updated_at).getTime())
        ]
      )

      const daysSinceActivity = (Date.now() - latestActivity) / (1000 * 60 * 60 * 24)

      if (daysSinceActivity > 14) {
        risks.push({
          title: `Project Stagnation - ${projectName}`,
          description: `No development activity for ${Math.ceil(daysSinceActivity)} days`,
          type: "warning",
          project: projectName,
          timestamp: new Date(latestActivity),
          severity: 2
        })
      }
    })

    // Process consolidated milestone risks
    Object.values(milestoneData).forEach((milestone) => {
      const atRiskProjects = milestone.projects.filter((p: { name: string, progress: number }) => p.progress < 80)

      // Calculate average progress for the milestone across all associated projects
      const totalProgressSum = milestone.projects.reduce((sum, p) => sum + p.progress, 0)
      const averageProgress = milestone.projects.length > 0 ? Math.round(totalProgressSum / milestone.projects.length) : 0

      if (milestone.isOverdue && averageProgress < 100) {
        const projectList = milestone.projects.map((p: { name: string, progress: number }) => `${p.name} (${p.progress}%)`).join(', ')
        risks.push({
          title: `Overdue Milestone - ${milestone.title}`,
          description: `${Math.abs(milestone.daysUntilDue)} days overdue across ${milestone.projects.length} projects (${averageProgress}% complete): ${projectList}`,
          type: "error",
          project: "Portfolio",
          timestamp: milestone.timestamp,
          severity: 5
        })
      } else if (!milestone.isOverdue && milestone.daysUntilDue <= 7 && atRiskProjects.length > 0) {
        const projectList = atRiskProjects.map((p: { name: string, progress: number }) => `${p.name} (${p.progress}%)`).join(', ')
        risks.push({
          title: `Milestone Delay Risk - ${milestone.title}`,
          description: `Due in ${milestone.daysUntilDue} days. At risk: ${projectList}`,
          type: milestone.daysUntilDue <= 3 ? "error" : "warning",
          project: "Portfolio",
          timestamp: milestone.timestamp,
          severity: milestone.daysUntilDue <= 3 ? 5 : 4
        })
      }
    })

    // Sort by severity (highest first) and then by timestamp (most recent first)
    return risks
      .sort((a, b) => {
        if (a.severity !== b.severity) return b.severity - a.severity
        return b.timestamp.getTime() - a.timestamp.getTime()
      })
      .slice(0, 6) // Show top 6 risks

  }, [projectsData])

  // Helper function to format time ago
  const formatTimeAgo = (date: Date) => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMs / 3600000)
    const diffDays = Math.floor(diffMs / 86400000)

    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    if (diffDays < 30) return `${diffDays}d ago`
    return date.toLocaleDateString()
  }

  return (
    <div className="grid gap-6">
      {/* Portfolio overview */}
      <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm overflow-hidden">
        <CardHeader className="border-b border-slate-700/50 pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-slate-100 flex items-center">
              <Github className="mr-2 h-5 w-5 text-cyan-500" />
              Portfolio Overview
            </CardTitle>
            <div className="flex items-center space-x-2">
              <select
                value={selectedMilestone}
                onChange={(e) => setSelectedMilestone(e.target.value)}
                className="bg-slate-800/50 border border-slate-700/50 rounded-md px-3 py-1.5 text-sm text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500/50 focus:border-cyan-500/50"
              >
                <option value="all">All Milestones</option>
                {allMilestones.map((milestone) => (
                  <option key={milestone} value={milestone}>
                    {milestone}
                  </option>
                ))}
              </select>
              <Button variant="ghost" size="icon" className="h-8 w-8 text-slate-400" onClick={refetch}>
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <MilestoneProgressCard
              milestoneProgress={filteredMilestoneProgress}
              idealMilestoneProgress={idealMilestoneProgress}
              selectedMilestone={selectedMilestone}
              variant="compact"
            />
            <MetricCard
              title="Team Velocity"
              value={teamVelocity}
              icon={TrendingUp}
              trend="stable"
              color="purple"
              detail={`${teamVelocity} issues/sprint avg`}
            />
            <MetricCard
              title="Issue Resolution"
              value={issueResolution}
              icon={CheckCircle}
              trend="up"
              color="blue"
              detail="overall completion rate"
            />
          </div>

          <div className="mt-8">
            <div className="flex items-center justify-between mb-4">
              <Tabs defaultValue="projects" className="flex-1">
                <TabsList className="bg-slate-800/50 p-1">
                  <TabsTrigger
                    value="projects"
                    className="data-[state=active]:bg-slate-700 data-[state=active]:text-cyan-400"
                  >
                    Projects
                  </TabsTrigger>
                  <TabsTrigger
                    value="velocity"
                    className="data-[state=active]:bg-slate-700 data-[state=active]:text-cyan-400"
                  >
                    Velocity
                  </TabsTrigger>
                  <TabsTrigger
                    value="issues"
                    className="data-[state=active]:bg-slate-700 data-[state=active]:text-cyan-400"
                  >
                    Issues
                  </TabsTrigger>
                </TabsList>
              </Tabs>

              {/* Move legend outside of Tabs component */}
              <div className="flex items-center space-x-2 text-xs text-slate-400">
                <div className="flex items-center">
                  <div className="h-2 w-2 rounded-full bg-green-500 mr-1"></div>
                  On Track
                </div>
                <div className="flex items-center">
                  <div className="h-2 w-2 rounded-full bg-amber-500 mr-1"></div>
                  At Risk
                </div>
                <div className="flex items-center">
                  <div className="h-2 w-2 rounded-full bg-red-500 mr-1"></div>
                  Behind
                </div>
              </div>
            </div>

            <Tabs defaultValue="projects" className="w-full">
              <TabsContent value="projects" className="mt-0">
                <div className="bg-slate-800/30 rounded-lg border border-slate-700/50 overflow-hidden">
                  <div className="grid grid-cols-12 text-xs text-slate-400 p-3 border-b border-slate-700/50 bg-slate-800/50">
                    <div className="col-span-3">Project</div>
                    <div className="col-span-2">Progress</div>
                    <div className="col-span-2">Issues</div>
                    <div className="col-span-2">PRs</div>
                    <div className="col-span-2">Team</div>
                    <div className="col-span-1">Status</div>
                  </div>

                  <div className="divide-y divide-slate-700/30">
                    {filteredProjects.map((project) => {
                      const filteredProgress = calculateFilteredProgress(project)
                      const teamCount = getProjectTeamCount(project)

                      return (
                        <ProjectRow
                          key={project.name}
                          name={project.name.charAt(0).toUpperCase() + project.name.slice(1)}
                          progress={filteredProgress}
                          issues={{
                            open: project.issues.filter((i: any) => i.state === "open").length,
                            closed: project.issues.filter((i: any) => i.state === "closed").length,
                          }}
                          prs={{
                            open: project.pullRequests.filter((pr: any) => pr.state === "open").length,
                            merged: project.pullRequests.filter((pr: any) => pr.state === "closed").length,
                          }}
                          team={teamCount}
                          status={calculateProjectStatus(project, filteredProgress)} // Use calculated status
                        />
                      )
                    })}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="velocity" className="mt-0">
                <div className="h-64 w-full relative bg-slate-800/30 rounded-lg border border-slate-700/50 overflow-hidden">
                  <VelocityChart />
                  <div className="absolute bottom-4 right-4 bg-slate-900/80 backdrop-blur-sm rounded-md px-3 py-2 border border-slate-700/50">
                    <div className="text-xs text-slate-400">Weekly Velocity</div>
                    <div className="text-lg font-mono text-cyan-400">{teamVelocity} pts</div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="issues" className="mt-0">
                <div className="bg-slate-800/30 rounded-lg border border-slate-700/50 p-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <IssueMetric
                      title="Open Issues"
                      count={filteredProjects.reduce(
                        (sum: number, p) => sum + p.issues.filter((i: any) => i.state === "open").length,
                        0,
                      )}
                      trend="down"
                      color="amber"
                    />
                    <IssueMetric
                      title="Closed This Week"
                      count={filteredProjects.reduce(
                        (sum: number, p) =>
                          sum +
                          p.issues.filter(
                            (i: any) =>
                              i.state === "closed" &&
                              new Date(i.closed_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                          ).length,
                        0,
                      )}
                      trend="up"
                      color="green"
                    />
                    <IssueMetric
                      title="In Review"
                      count={filteredProjects.reduce(
                        (sum: number, p) =>
                          sum +
                          p.pullRequests.filter((pr: any) => pr.state === "open" && pr.requested_reviewers.length > 0)
                            .length,
                        0,
                      )}
                      trend="stable"
                      color="blue"
                    />
                    <IssueMetric
                      title="Blocked"
                      count={filteredProjects.reduce(
                        (sum: number, p) =>
                          sum +
                          p.issues.filter((i: any) => i.labels.some((l: any) => l.name === "blocked")).length,
                        0,
                      )}
                      trend="up"
                      color="red"
                    />
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </CardContent>
      </Card>

      {/* Team Performance & Risk Alerts */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-slate-100 flex items-center text-base">
              <TrendingUp className="mr-2 h-5 w-5 text-green-500" />
              Development Team Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoadingTeam ? (
              <div className="text-center text-slate-400 py-4">Loading team data...</div>
            ) : enhancedTeamData?.developers?.length > 0 ? (
              <div className="space-y-4">
                {enhancedTeamData.developers
                  .sort((a: any, b: any) => b.velocity - a.velocity)
                  .slice(0, 6).map((member: any) => (
                  <RealTeamMember
                    key={member.login}
                    name={member.login}
                    role="Developer"
                    commits={member.commits}
                    reviews={member.reviews}
                    velocity={member.velocity}
                    avatar={member.avatar_url}
                    projects={member.projects}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center text-slate-400 py-4">No team data available</div>
            )}
          </CardContent>
        </Card>

        <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-slate-100 flex items-center text-base">
              <Target className="mr-2 h-5 w-5 text-amber-500" />
              Risk Alerts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {calculateRiskAlerts.length > 0 ? (
                calculateRiskAlerts.map((risk, index) => (
                  <AlertItem
                    key={`${risk.project}-${index}`}
                    title={risk.title}
                    time={formatTimeAgo(risk.timestamp)}
                    description={risk.description}
                    type={risk.type}
                    project={risk.project}
                  />
                ))
              ) : (
                <div className="text-center text-green-400 py-4">
                  <CheckCircle className="h-8 w-8 mx-auto mb-2" />
                  <p className="text-sm">All projects on track - no critical risks detected</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* GitHub Activity Feed */}
      <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader className="pb-2 flex flex-row items-center justify-between">
          <CardTitle className="text-slate-100 flex items-center text-base">
            <Activity className="mr-2 h-5 w-5 text-blue-500" />
            Recent Activity
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="bg-slate-800/50 text-blue-400 border-blue-500/50">
              Live Feed
            </Badge>
            <Button variant="ghost" size="icon" className="h-8 w-8 text-slate-400">
              <Filter className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {projectsData.slice(0, 5).map((project, index) => {
              const latestCommit = project.commits[0]
              if (!latestCommit) return null

              return (
                <ActivityItem
                  key={index}
                  type="commit"
                  user={latestCommit.author?.login || latestCommit.commit.author.name}
                  action="pushed commits to"
                  target={`${project.name}/main`}
                  time={new Date(latestCommit.commit.author.date).toLocaleString()}
                  project={project.name}
                />
              )
            })}
          </div>
        </CardContent>
        <CardFooter className="border-t border-slate-700/50 pt-4">
          <div className="flex items-center justify-between w-full">
            <Button variant="outline" size="sm" className="border-slate-700 bg-slate-800/50 hover:bg-slate-700/50">
              <Eye className="h-4 w-4 mr-2" />
              View All Activity
            </Button>
            <div className="flex items-center space-x-2">
              <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                <Github className="h-4 w-4 mr-2" />
                Open GitHub
              </Button>
            </div>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}