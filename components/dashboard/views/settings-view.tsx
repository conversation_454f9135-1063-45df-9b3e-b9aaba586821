import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { SettingToggle } from "@/components/dashboard/shared/setting-toggle"
import { Settings, RefreshCw } from "lucide-react"

export function SettingsView() {
  return (
    <div className="grid gap-6">
      <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-slate-100 flex items-center">
            <Settings className="mr-2 h-5 w-5 text-cyan-500" />
            Dashboard Settings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-slate-200 mb-4">Notifications</h3>
              <div className="space-y-4">
                <SettingToggle
                  label="Real-time Alerts"
                  description="Receive instant notifications for critical issues"
                  defaultChecked={true}
                />
                <SettingToggle
                  label="Daily Digest"
                  description="Get a daily summary of project activities"
                  defaultChecked={true}
                />
                <SettingToggle
                  label="Milestone Reminders"
                  description="Notifications for upcoming milestone deadlines"
                  defaultChecked={false}
                />
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-slate-200 mb-4">Data Sync</h3>
              <div className="space-y-4">
                <SettingToggle
                  label="Auto Sync"
                  description="Automatically sync data from GitHub every 5 minutes"
                  defaultChecked={true}
                />
                <SettingToggle
                  label="Webhook Integration"
                  description="Use webhooks for real-time updates"
                  defaultChecked={false}
                />
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-slate-200 mb-4">Display</h3>
              <div className="space-y-4">
                <SettingToggle label="Dark Mode" description="Use dark theme for the dashboard" defaultChecked={true} />
                <SettingToggle
                  label="Compact View"
                  description="Show more information in less space"
                  defaultChecked={false}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-slate-100 text-base">GitHub Integration</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-slate-800/50 rounded-lg border border-slate-700/50">
              <div>
                <div className="text-sm font-medium text-slate-200">GitHub Token</div>
                <div className="text-xs text-slate-400">Connected to berrijam organization</div>
              </div>
              <Badge className="bg-green-500/20 text-green-400 border-green-500/50">Connected</Badge>
            </div>

            <div className="flex items-center justify-between p-4 bg-slate-800/50 rounded-lg border border-slate-700/50">
              <div>
                <div className="text-sm font-medium text-slate-200">Webhook Status</div>
                <div className="text-xs text-slate-400">Real-time event notifications</div>
              </div>
              <Badge className="bg-amber-500/20 text-amber-400 border-amber-500/50">Pending</Badge>
            </div>

            <Button className="w-full bg-blue-600 hover:bg-blue-700">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Connection
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}