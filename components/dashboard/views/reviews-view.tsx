import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { useGitHubData } from "@/hooks/use-github-data"
import { MetricCard } from "@/components/dashboard/cards/metric-card"
import { ReviewRow } from "@/components/dashboard/shared/review-row"
import { Eye, CheckCircle, GitPullRequest, MessageSquare } from "lucide-react"

export function ReviewsView() {
  const { projectsData } = useGitHubData()

  const allPRs = projectsData.flatMap((project) =>
    project.pullRequests.map((pr: any) => ({
      ...pr,
      projectName: project.name,
    })),
  )

  const openPRs = allPRs.filter((pr) => pr.state === "open")
  const mergedPRs = allPRs.filter((pr) => pr.merged_at !== null)
  const pendingReviews = openPRs.filter((pr) => pr.requested_reviewers.length > 0)

  return (
    <div className="grid gap-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <MetricCard
          title="Pending Reviews"
          value={pendingReviews.length}
          icon={Eye}
          trend="down"
          color="amber"
          detail="Awaiting review"
        />
        <MetricCard
          title="Merged PRs"
          value={mergedPRs.length}
          icon={CheckCircle}
          trend="up"
          color="green"
          detail="All time"
        />
        <MetricCard
          title="Open PRs"
          value={openPRs.length}
          icon={GitPullRequest}
          trend="stable"
          color="blue"
          detail="Currently open"
        />
      </div>

      <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-slate-100 flex items-center">
            <MessageSquare className="mr-2 h-5 w-5 text-cyan-500" />
            Recent Pull Requests
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {allPRs.slice(0, 10).map((pr) => (
              <ReviewRow
                key={pr.id}
                pr={`#${pr.number}`}
                title={pr.title}
                author={pr.user.login}
                reviewer={pr.requested_reviewers[0]?.login || "No reviewer"}
                project={pr.projectName}
                status={pr.merged_at ? "approved" : pr.state === "open" ? "pending" : "changes-requested"}
                created={new Date(pr.created_at).toLocaleDateString()}
                changes={`+${pr.additions} -${pr.deletions}`}
              />
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}