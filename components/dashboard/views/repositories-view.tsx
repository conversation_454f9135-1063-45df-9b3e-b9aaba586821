import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card"
import { RepositoryCard } from "@/components/dashboard/cards/repository-card"
import { GitBranch } from "lucide-react"

interface RepositoriesViewProps {
  projectsData: any[]
}

export function RepositoriesView({ projectsData }: RepositoriesViewProps) {
  return (
    <div className="grid gap-6">
      <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-slate-100 flex items-center">
            <GitBranch className="mr-2 h-5 w-5 text-cyan-500" />
            Repository Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {projectsData.map((project) => (
              <RepositoryCard
                key={project.repo.id}
                name={project.repo.full_name}
                description={project.repo.description || `${project.name} project repository`}
                language={project.repo.language || "Unknown"}
                stars={project.repo.stargazers_count}
                forks={project.repo.forks_count}
                issues={project.issues.filter((i: { state: string }) => i.state === "open").length}
                prs={project.pullRequests.filter((pr: { state: string }) => pr.state === "open").length}
                lastCommit={new Date(project.repo.pushed_at).toLocaleDateString()}
                status={project.status === "on-track" ? "active" : project.status === "at-risk" ? "behind" : "critical"}
              />
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}