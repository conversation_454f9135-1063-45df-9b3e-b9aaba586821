import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { useGitHubData, useGitHubAnalytics } from "@/hooks/use-github-data"
import { MetricCard } from "@/components/dashboard/cards/metric-card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { DeliveryVelocityTrends } from "@/components/dashboard/charts/delivery-velocity-trends"
import { BurndownBurnupChart } from "@/components/dashboard/charts/burndown-burnup-chart"
import {
  Activity,
  AlertCircle,
  ArrowDown,
  ArrowUp,
  BarChart3,
  CheckCircle,
  Clock,
  GitCommit,
  LineChart,
  Shield,
  Target,
  TrendingUp,
  Users,
  Zap
} from "lucide-react"

// Helper function to calculate trend
function calculateTrend(current: number, previous: number): "up" | "down" | "stable" {
  const diff = current - previous
  if (Math.abs(diff) < 2) return "stable"
  return diff > 0 ? "up" : "down"
}

// Helper function to get risk level color
function getRiskLevelColor(level: "low" | "medium" | "high"): string {
  switch (level) {
    case "low":
      return "bg-green-500/20 text-green-400 border-green-500/50"
    case "medium":
      return "bg-amber-500/20 text-amber-400 border-amber-500/50"
    case "high":
      return "bg-red-500/20 text-red-400 border-red-500/50"
  }
}

// Risk Heatmap Matrix Component
function RiskHeatmapMatrix({ projects }: { projects: any[] }) {
  const getCurrentMilestoneProgress = (project: any, currentMilestone: number = 2): number => {
    // Get issues for the current milestone
    const currentMilestoneIssues = project.issues.filter((issue: any) =>
      issue.milestone?.title?.includes(`Milestone ${currentMilestone}`)
    )

    if (currentMilestoneIssues.length === 0) return 100 // No issues = complete

    const closedIssues = currentMilestoneIssues.filter((issue: any) => issue.state === "closed").length
    return Math.round((closedIssues / currentMilestoneIssues.length) * 100)
  }

  const getRiskLevel = (project: any): "low" | "medium" | "high" => {
    const currentProgress = getCurrentMilestoneProgress(project, 2) // Milestone 2 progress
    const openIssues = project.issues.filter((i: any) => i.state === "open").length

    // LOW RISK: Current milestone complete or nearly complete
    if (currentProgress >= 90) return "low"

    // HIGH RISK: Very low progress or too many open issues
    if (currentProgress < 40 || openIssues > 12) return "high"

    // MEDIUM RISK: Everything else
    return "medium"
  }

  const getImpactLevel = (project: any): "low" | "medium" | "high" => {
    const currentProgress = getCurrentMilestoneProgress(project, 2)
    const openIssues = project.issues.filter((i: any) => i.state === "open").length

    // LOW IMPACT: Milestone complete or consistently performing well
    if (currentProgress >= 90 || (currentProgress >= 70 && openIssues <= 5)) return "low"

    // HIGH IMPACT: Critical delays that could affect portfolio delivery
    if (currentProgress < 30 || openIssues > 15) return "high"

    // MEDIUM IMPACT: Some concerns but manageable
    return "medium"
  }

  type MatrixType = {
    [key in "low" | "medium" | "high"]: {
      [key in "low" | "medium" | "high"]: any[]
    }
  }

  const matrix: MatrixType = {
    low: { low: [], medium: [], high: [] },
    medium: { low: [], medium: [], high: [] },
    high: { low: [], medium: [], high: [] },
  }

  projects.forEach((project) => {
    const risk = getRiskLevel(project)
    const impact = getImpactLevel(project)
    matrix[risk][impact].push(project)
  })

  return (
    <div className="grid grid-cols-3 gap-2">
      {Object.entries(matrix).map(([risk, impacts]) => (
        Object.entries(impacts).map(([impact, projects]) => (
          <div
            key={`${risk}-${impact}`}
            className={`p-2 rounded-lg border ${
              risk === "high" || impact === "high"
                ? "bg-red-500/10 border-red-500/30"
                : risk === "medium" || impact === "medium"
                ? "bg-amber-500/10 border-amber-500/30"
                : "bg-green-500/10 border-green-500/30"
            }`}
          >
            <div className="text-xs text-slate-400 mb-2">
              {risk.toUpperCase()} Risk / {impact.toUpperCase()} Impact
            </div>
            <div className="space-y-1">
              {projects.map((project: any) => {
                const progress = getCurrentMilestoneProgress(project, 2)
                return (
                  <div
                    key={project.name}
                    className="text-xs px-2 py-1 rounded bg-slate-800/50 border border-slate-700/50"
                  >
                    {project.name} ({progress}%)
                  </div>
                )
              })}
            </div>
          </div>
        ))
      ))}
    </div>
  )
}

// Predictive Analytics Component
function PredictiveAnalytics({ projects }: { projects: any[] }) {
  // Calculate predictive metrics
  const totalIssues = projects.reduce((sum, p) => sum + p.issues.length, 0)
  const openIssues = projects.reduce((sum, p) => sum + p.issues.filter((i: any) => i.state === "open").length, 0)
  const totalPRs = projects.reduce((sum, p) => sum + p.pullRequests.length, 0)
  const openPRs = projects.reduce((sum, p) => sum + p.pullRequests.filter((pr: any) => pr.state === "open").length, 0)

  const issueResolutionRate = totalIssues > 0 ? (totalIssues - openIssues) / totalIssues : 0
  const prMergeRate = totalPRs > 0 ? (totalPRs - openPRs) / totalPRs : 0

  const estimatedDeliveryDate = new Date()
  estimatedDeliveryDate.setDate(estimatedDeliveryDate.getDate() + Math.ceil(openIssues / (issueResolutionRate * 5)))

  const budgetBurnRate = 85 // Example fixed rate
  const riskPrediction = Math.min(100, Math.max(0, (openIssues / totalIssues) * 100))

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-3">
        <div className="p-3 rounded-lg bg-slate-800/50 border border-slate-700/50">
          <div className="text-xs text-slate-500 mb-1">Estimated Delivery</div>
          <div className="text-sm font-mono text-cyan-400">
            {estimatedDeliveryDate.toLocaleDateString()}
          </div>
          <div className="text-xs text-slate-400 mt-1">Based on current velocity</div>
        </div>
        <div className="p-3 rounded-lg bg-slate-800/50 border border-slate-700/50">
          <div className="text-xs text-slate-500 mb-1">Budget Burn Rate</div>
          <div className="text-sm font-mono text-cyan-400">{budgetBurnRate}%</div>
          <div className="text-xs text-slate-400 mt-1">Monthly projection</div>
        </div>
      </div>
      <div className="p-3 rounded-lg bg-slate-800/50 border border-slate-700/50">
        <div className="text-xs text-slate-500 mb-1">Risk Prediction</div>
        <div className="flex items-center justify-between mb-2">
          <div className="text-sm font-mono text-cyan-400">{Math.round(riskPrediction)}%</div>
          <Badge
            variant="outline"
            className={`${
              riskPrediction > 75
                ? "bg-red-500/20 text-red-400 border-red-500/50"
                : riskPrediction > 50
                ? "bg-amber-500/20 text-amber-400 border-amber-500/50"
                : "bg-green-500/20 text-green-400 border-green-500/50"
            }`}
          >
            {riskPrediction > 75 ? "High" : riskPrediction > 50 ? "Medium" : "Low"}
          </Badge>
        </div>
        <Progress value={riskPrediction} className="h-1.5 bg-slate-700">
          <div
            className={`h-full rounded-full ${
              riskPrediction > 75
                ? "bg-red-500"
                : riskPrediction > 50
                ? "bg-amber-500"
                : "bg-green-500"
            }`}
            style={{ width: `${riskPrediction}%` }}
          />
        </Progress>
      </div>
    </div>
  )
}

export function AnalyticsView() {
  const { projectsData, isLoading: isLoadingData } = useGitHubData()
  const { data: analyticsData, isLoading: isLoadingAnalytics } = useGitHubAnalytics()

  if (isLoadingData || isLoadingAnalytics) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-cyan-500 font-mono text-sm">Loading executive dashboard...</div>
      </div>
    )
  }

  // FIXED: More reasonable project status logic
  const calculateProjectStatus = (project: any): "on-track" | "at-risk" | "behind" => {
    const progress = project.progress
    const openIssues = project.issues.filter((i: any) => i.state === "open").length
    const overdueMilestones = project.milestones?.filter((m: any) =>
      m && m.due_on && new Date(m.due_on) < new Date() && m.state === "open"
    ).length || 0

    // Behind: Overdue milestones or very low progress
    if (overdueMilestones > 0 || progress < 25) return "behind"

    // At Risk: Low progress or many open issues
    if (progress < 60 || openIssues > 10) return "at-risk"

    // On Track: Good progress and manageable issues
    return "on-track"
  }

  // Calculate executive metrics with FIXES
  const portfolioHealth = Math.round(
    projectsData.reduce((sum, p) => sum + p.progress, 0) / projectsData.length
  )

  // FIXED: Calculate actual delivery confidence with better status logic
  const projectsWithStatus = projectsData.map(project => ({
    ...project,
    calculatedStatus: calculateProjectStatus(project)
  }))

  // FIXED: Calculate milestones at risk dynamically
  const milestonesAtRisk = projectsData.reduce((total, project) => {
    const atRiskMilestones = project.milestones?.filter((milestone: any) => {
      if (!milestone || !milestone.due_on) return false

      const dueDate = new Date(milestone.due_on)
      const now = new Date()
      const daysUntilDue = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

      // At risk if: due within 7 days OR overdue, and not completed
      return (daysUntilDue <= 7 || daysUntilDue < 0) && milestone.state === "open"
    }) || []

    return total + atRiskMilestones.length
  }, 0)

  const totalMilestones = projectsData.reduce((sum, p) => sum + (p.milestones?.length || 0), 0)

  // FIXED: Calculate business value - count actual features, not bug fixes
  const businessValue = projectsData.reduce((sum, project) => {
    const featurePRs = project.pullRequests.filter((pr: any) => {
      // Only count merged PRs
      if (!pr.merged_at) return false

      // Filter out bug fixes and maintenance
      const isBugFix = pr.labels?.some((label: any) =>
        label.name.toLowerCase().includes('bug') ||
        label.name.toLowerCase().includes('fix') ||
        label.name.toLowerCase().includes('hotfix')
      ) || false

      const isMaintenance = pr.labels?.some((label: any) =>
        label.name.toLowerCase().includes('maintenance') ||
        label.name.toLowerCase().includes('chore') ||
        label.name.toLowerCase().includes('deps')
      ) || false

      // Only count if it's likely a feature (not a bug fix or maintenance)
      return !isBugFix && !isMaintenance
    })

    return sum + featurePRs.length
  }, 0)

  return (
    <div className="grid gap-6">
      {/* Row 1: FIXED Executive Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <MetricCard
          title="Portfolio Health"
          value={portfolioHealth}
          icon={Shield}
          trend={calculateTrend(portfolioHealth, 75)}
          color="green"
          detail={`${projectsData.length} projects tracked`}
        />
        <MetricCard
          title="Business Value"
          value={businessValue}
          icon={TrendingUp}
          trend={calculateTrend(businessValue, 100)}
          color="purple"
          detail={`${businessValue} feature${businessValue !== 1 ? 's' : ''} shipped this quarter`}
          showPercentage={false}
        />
      </div>

      {/* Row 2: Risk & Status Overview */}
      <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader className="pb-2">
          <CardTitle className="text-slate-100 flex items-center text-base">
            <AlertCircle className="mr-2 h-5 w-5 text-amber-500" />
            Risk Heatmap Matrix
          </CardTitle>
        </CardHeader>
        <CardContent>
          <RiskHeatmapMatrix projects={projectsData} />
        </CardContent>
      </Card>

      {/* Row 3: Performance & Trend Analysis */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-slate-100 flex items-center text-base">
              <LineChart className="mr-2 h-5 w-5 text-cyan-500" />
              Delivery Velocity Trends
            </CardTitle>
          </CardHeader>
          <CardContent>
            <DeliveryVelocityTrends projects={projectsData} />
          </CardContent>
        </Card>

        <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-slate-100 flex items-center text-base">
              <Activity className="mr-2 h-5 w-5 text-purple-500" />
              Burndown/burnup charts for delivery tracking
            </CardTitle>
          </CardHeader>
          <CardContent>
            <BurndownBurnupChart projects={projectsData} />
          </CardContent>
        </Card>
      </div>

      {/* Row 4: Predictive Analytics */}
      <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader className="pb-2">
          <CardTitle className="text-slate-100 flex items-center text-base">
            <Zap className="mr-2 h-5 w-5 text-amber-500" />
            Predictive Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <PredictiveAnalytics projects={projectsData} />
        </CardContent>
      </Card>
    </div>
  )
}