import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { useGitHubData } from "@/hooks/use-github-data"
import { IssueMetric } from "@/components/dashboard/cards/issue-metric"
import { IssueRow } from "@/components/dashboard/shared/issue-row"
import { AlertCircle } from "lucide-react"

export function IssuesView() {
  const { projectsData } = useGitHubData()

  const allIssues = projectsData.flatMap((project) =>
    project.issues.map((issue: any) => ({
      ...issue,
      projectName: project.name,
    })),
  )

  const openIssues = allIssues.filter((issue) => issue.state === "open")
  const closedIssues = allIssues.filter((issue) => issue.state === "closed")
  const highPriorityIssues = allIssues.filter((issue) =>
    issue.labels.some(
      (label: any) => label.name.toLowerCase().includes("priority") || label.name.toLowerCase().includes("urgent"),
    ),
  )

  return (
    <div className="grid gap-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <IssueMetric title="Open Issues" count={openIssues.length} trend="down" color="amber" />
        <IssueMetric title="Closed Issues" count={closedIssues.length} trend="up" color="green" />
        <IssueMetric title="High Priority" count={highPriorityIssues.length} trend="stable" color="red" />
        <IssueMetric title="Total Issues" count={allIssues.length} trend="up" color="blue" />
      </div>

      <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-slate-100 flex items-center">
            <AlertCircle className="mr-2 h-5 w-5 text-cyan-500" />
            Recent Issues
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {allIssues.slice(0, 10).map((issue) => (
              <IssueRow
                key={issue.id}
                id={`#${issue.number}`}
                title={issue.title}
                project={issue.projectName}
                assignee={issue.assignee?.login || "Unassigned"}
                priority={
                  issue.labels.some(
                    (label: any) =>
                      label.name.toLowerCase().includes("high") || label.name.toLowerCase().includes("urgent"),
                  )
                    ? "high"
                    : issue.labels.some((label: any) => label.name.toLowerCase().includes("medium"))
                      ? "medium"
                      : "low"
                }
                status={
                  issue.state === "open"
                    ? "open"
                    : issue.state === "in_progress"
                      ? "in-progress"
                      : issue.state === "in_review"
                        ? "in-review"
                        : "blocked"
                }
                created={new Date(issue.created_at).toLocaleDateString()}
              />
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}