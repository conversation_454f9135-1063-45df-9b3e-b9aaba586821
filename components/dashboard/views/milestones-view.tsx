import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card"
import { useGitHubMilestones } from "@/hooks/use-github-data"
import { GanttChart } from "@/components/gantt-chart"
import { ProjectMilestoneProgress } from "@/components/dashboard/cards/project-milestone-progress"

export function MilestonesView() {
  const { data: milestonesData, isLoading, error } = useGitHubMilestones()

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-cyan-500 font-mono text-sm">Loading milestones...</div>
      </div>
    )
  }

  if (error) {
    return <div className="text-red-400 text-center py-8">Error loading milestones: {error}</div>
  }

  // Provide fallback data if milestonesData is undefined
  const stats = milestonesData?.stats || {
    total: 0,
    closed: 0,
    upcoming: 0,
    overdue: 0,
  }

  const projectsData = milestonesData?.projectsData || []
  const milestones = milestonesData?.milestones || []

  return (
    <div className="grid gap-6">
      {/* Gantt Chart */}
      <GanttChart milestones={milestones} />

      {/* Milestone Statistics */}
      <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-slate-100 text-base">Milestone Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-cyan-400">{stats.total}</div>
              <div className="text-sm text-slate-400">Total</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">{stats.closed}</div>
              <div className="text-sm text-slate-400">Completed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-amber-400">{stats.upcoming}</div>
              <div className="text-sm text-slate-400">Due Soon</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-400">{stats.overdue}</div>
              <div className="text-sm text-slate-400">Overdue</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Project Milestone Progress using real data */}
      <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-slate-100 text-base">Milestone Progress by Project</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {projectsData.length > 0 ? (
              projectsData.map((project: any) => {
                const projectMilestones = project.milestones || []
                const completedMilestones = projectMilestones.filter((m: any) => m && m.state === "closed").length
                const progress =
                  projectMilestones.length > 0 ? Math.round((completedMilestones / projectMilestones.length) * 100) : 0

                return (
                  <ProjectMilestoneProgress
                    key={project.name}
                    project={project.name.charAt(0).toUpperCase() + project.name.slice(1)}
                    milestone={2}
                    progress={progress}
                  />
                )
              })
            ) : (
              <div className="text-center text-slate-400 py-4">No milestone data available</div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 