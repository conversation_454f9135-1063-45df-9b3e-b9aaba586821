import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { DetailedTeamMember } from "@/components/dashboard/shared/team-members"
import { Users } from "lucide-react"

interface TeamsViewProps {
  enhancedTeamData: any
  isLoading: boolean
  error: any
}

export function TeamsView({ enhancedTeamData, isLoading, error }: TeamsViewProps) {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-cyan-500 font-mono text-sm">Loading team data...</div>
      </div>
    )
  }

  if (error) {
    return <div className="text-red-400 text-center py-8">Error loading team data: {error}</div>
  }

  return (
    <div className="grid gap-6">
      {/* Development Team */}
      <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-slate-100 flex items-center">
            <Users className="mr-2 h-5 w-5 text-cyan-500" />
            Development Team ({enhancedTeamData?.developers?.length || 0})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {enhancedTeamData?.developers?.map((member: any) => (
              <DetailedTeamMember
                key={member.login}
                name={member.login}
                role="Developer"
                team="Development"
                commits={member.commits}
                reviews={member.reviews}
                velocity={member.velocity}
                projects={member.projects}
                avatar={member.avatar_url}
              />
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Leadership Team */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Reviewers */}
        <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-slate-100 text-base">Code Reviewers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {enhancedTeamData?.reviewers?.map((member: any) => (
                <div key={member.login} className="flex items-center space-x-3">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={member.avatar_url || "/placeholder.svg"} alt={member.login} />
                    <AvatarFallback className="bg-slate-700 text-purple-400">
                      {member.login.slice(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="text-sm font-medium text-slate-200">{member.login}</div>
                    <div className="text-xs text-slate-400">{member.reviews} reviews</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Project Manager */}
        <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-slate-100 text-base">Project Manager</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {enhancedTeamData?.pm?.map((member: any) => (
                <div key={member.login} className="flex items-center space-x-3">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={member.avatar_url || "/placeholder.svg"} alt={member.login} />
                    <AvatarFallback className="bg-slate-700 text-green-400">
                      {member.login.slice(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="text-sm font-medium text-slate-200">{member.login}</div>
                    <div className="text-xs text-slate-400">Project Manager</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Supervisor */}
        <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-slate-100 text-base">Supervisor</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {enhancedTeamData?.supervisor?.map((member: any) => (
                <div key={member.login} className="flex items-center space-x-3">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={member.avatar_url || "/placeholder.svg"} alt={member.login} />
                    <AvatarFallback className="bg-slate-700 text-amber-400">
                      {member.login.slice(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="text-sm font-medium text-slate-200">{member.login}</div>
                    <div className="text-xs text-slate-400">Internal Supervisor</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}