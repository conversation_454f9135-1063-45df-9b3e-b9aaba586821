"use client"

import { useEffect, useState, useRef, useMemo } from "react"
import {
  Activity,
  AlertCircle,
  Bell,
  Calendar,
  Command,
  Download,
  FileText,
  Github,
  Hexagon,
  MessageSquare,
  Moon,
  RefreshCw,
  Search,
  Settings,
  Star,
  Sun,
  Target,
  Terminal,
  Users,
  Zap,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { useGitHubData, useEnhancedTeamData } from "@/hooks/use-github-data"

// Import all view components
import { PortfolioView } from "@/components/dashboard/views/portfolio-view"
import { MilestonesView } from "@/components/dashboard/views/milestones-view"
import { AnalyticsView } from "@/components/dashboard/views/analytics-view"
import { TeamsView } from "@/components/dashboard/views/teams-view"
import { IssuesView } from "@/components/dashboard/views/issues-view"
import { ReviewsView } from "@/components/dashboard/views/reviews-view"
import { RepositoriesView } from "@/components/dashboard/views/repositories-view"
import { SettingsView } from "@/components/dashboard/views/settings-view"

// Import extracted shared components
import { NavItem } from "@/components/dashboard/shared/nav-item"
import { StatusItem } from "@/components/dashboard/shared/status-item"
import { ActionButton } from "@/components/dashboard/shared/action-button"

type ViewType = "portfolio" | "milestones" | "teams" | "repositories" | "analytics" | "issues" | "reviews" | "settings"

interface DashboardData {
  portfolioHealth: number
  milestoneProgress: number
  idealMilestoneProgress: number
  teamVelocity: number
  issueResolution: number
  projectsData: any[]
  lastUpdated: Date | null
}

export default function GitHubDashboard() {
  const [theme, setTheme] = useState<"dark" | "light">("dark")
  const [currentView, setCurrentView] = useState<ViewType>("portfolio")
  const [currentTime, setCurrentTime] = useState(new Date())
  const [isLoading, setIsLoading] = useState(true)
  const [selectedMilestone, setSelectedMilestone] = useState<string>("all")

  const {
    portfolioHealth,
    milestoneProgress,
    idealMilestoneProgress,
    teamVelocity,
    issueResolution,
    projectsData,
    isLoading: isLoadingData,
    error,
    refetch,
    lastUpdated
  } = useGitHubData()

  const { data: enhancedTeamData, isLoading: isLoadingTeam } = useEnhancedTeamData()

  const canvasRef = useRef<HTMLCanvasElement>(null)

  // Simulate initial loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 2000)

    return () => clearTimeout(timer)
  }, [])

  // Update time
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(interval)
  }, [])

  // Toggle theme
  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark")
  }

  // Format time
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("en-US", {
      hour12: false,
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    })
  }

  // Format date
  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  // Calculate days elapsed and current milestone dynamically
  const projectMetrics = useMemo(() => {
    const startDate = new Date("2025-05-02")
    const daysElapsed = Math.floor((currentTime.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))

    // Each milestone is 2 weeks (14 days)
    const getCurrentMilestone = (days: number) => {
      if (days <= 0) return 1
      return Math.min(Math.ceil(days / 14), 6) // Cap at milestone 6
    }

    const currentMilestone = getCurrentMilestone(daysElapsed)

    // Calculate the expected completion percentage for the current milestone
    // If we're in milestone 3, we should be 50% complete overall (3/6 = 50%)
    const expectedCompletionForCurrentMilestone = (currentMilestone / 6) * 100

    return {
      daysElapsed,
      currentMilestone,
      expectedCompletionForCurrentMilestone
    }
  }, [currentTime])

  // Update idealMilestoneProgress to use dynamic calculation
  const dynamicIdealMilestoneProgress = projectMetrics.expectedCompletionForCurrentMilestone

  const renderMainContent = () => {
    switch (currentView) {
      case "portfolio":
        return (
          <PortfolioView
            portfolioHealth={portfolioHealth}
            milestoneProgress={milestoneProgress}
            idealMilestoneProgress={dynamicIdealMilestoneProgress}
            teamVelocity={teamVelocity}
            issueResolution={issueResolution}
            projectsData={projectsData}
            refetch={refetch}
            selectedMilestone={selectedMilestone}
            setSelectedMilestone={setSelectedMilestone}
            enhancedTeamData={enhancedTeamData}
            isLoadingTeam={isLoadingTeam}
          />
        )
      case "milestones":
        return <MilestonesView />
      case "teams":
        return <TeamsView enhancedTeamData={enhancedTeamData} isLoading={isLoadingTeam} error={error} />
      case "repositories":
        return <RepositoriesView projectsData={projectsData} />
      case "analytics":
        return <AnalyticsView />
      case "issues":
        return <IssuesView />
      case "reviews":
        return <ReviewsView />
      case "settings":
        return <SettingsView />
      default:
        return (
          <PortfolioView
            portfolioHealth={portfolioHealth}
            milestoneProgress={milestoneProgress}
            idealMilestoneProgress={dynamicIdealMilestoneProgress}
            teamVelocity={teamVelocity}
            issueResolution={issueResolution}
            projectsData={projectsData}
            refetch={refetch}
            selectedMilestone={selectedMilestone}
            setSelectedMilestone={setSelectedMilestone}
            enhancedTeamData={enhancedTeamData}
            isLoadingTeam={isLoadingTeam}
          />
        )
    }
  }

  // Fix the type for the reduce function
  const totalOpenPRs = projectsData.reduce((sum: number, p: any) =>
    sum + p.pullRequests.filter((pr: any) => pr.state === "open").length,
    0
  )

  return (
    <div
      className={`${theme} min-h-screen bg-gradient-to-br from-black to-slate-900 text-slate-100 relative overflow-hidden`}
    >
      {/* Loading overlay */}
      {(isLoading || isLoadingData) && (
        <div className="absolute inset-0 bg-black/80 flex items-center justify-center z-50">
          <div className="flex flex-col items-center">
            <div className="relative w-24 h-24">
              <div className="absolute inset-0 border-4 border-cyan-500/30 rounded-full animate-ping"></div>
              <div className="absolute inset-2 border-4 border-t-cyan-500 border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin"></div>
              <div className="absolute inset-4 border-4 border-r-purple-500 border-t-transparent border-b-transparent border-l-transparent rounded-full animate-spin-slow"></div>
              <div className="absolute inset-6 border-4 border-b-blue-500 border-t-transparent border-r-transparent border-l-transparent rounded-full animate-spin-slower"></div>
              <div className="absolute inset-8 border-4 border-l-green-500 border-t-transparent border-r-transparent border-b-transparent rounded-full animate-spin"></div>
            </div>
            <div className="mt-4 text-cyan-500 font-mono text-sm tracking-wider">LOADING PORTFOLIO DATA</div>
          </div>
        </div>
      )}

      {error && (
        <div className="absolute inset-0 bg-black/80 flex items-center justify-center z-50">
          <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-6 max-w-md">
            <h3 className="text-red-400 font-medium mb-2">Error Loading Data</h3>
            <p className="text-slate-300 text-sm mb-4">{error}</p>
            <Button onClick={refetch} className="bg-red-600 hover:bg-red-700">
              Retry
            </Button>
          </div>
        </div>
      )}

      <div className="w-full px-4 relative z-10">
        {/* Header */}
        <header className="flex items-center justify-between py-4 border-b border-slate-700/50 mb-6">
          <div className="flex items-center space-x-2">
            <Github className="h-8 w-8 text-cyan-500" />
            <span className="text-xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent">
              BERRIJAM PORTFOLIO
            </span>
          </div>

          <div className="flex items-center space-x-6">
            <div className="hidden md:flex items-center space-x-1 bg-slate-800/50 rounded-full px-3 py-1.5 border border-slate-700/50 backdrop-blur-sm">
              <Search className="h-4 w-4 text-slate-400" />
              <input
                type="text"
                placeholder="Search projects..."
                className="bg-transparent border-none focus:outline-none text-sm w-40 placeholder:text-slate-500"
              />
            </div>

            <div className="flex items-center space-x-3">
              {lastUpdated && (
                <div className="text-xs text-slate-500">Last updated: {lastUpdated.toLocaleTimeString()}</div>
              )}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" className="relative text-slate-400 hover:text-slate-100">
                      <Bell className="h-5 w-5" />
                      <span className="absolute -top-1 -right-1 h-2 w-2 bg-cyan-500 rounded-full animate-pulse"></span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>GitHub Notifications</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={toggleTheme}
                      className="text-slate-400 hover:text-slate-100"
                    >
                      {theme === "dark" ? <Moon className="h-5 w-5" /> : <Sun className="h-5 w-5" />}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Toggle theme</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <Avatar>
                <AvatarImage src="/placeholder.svg?height=40&width=40" alt="Executive" />
                <AvatarFallback className="bg-slate-700 text-cyan-500">EX</AvatarFallback>
              </Avatar>
            </div>
          </div>
        </header>

        {/* Main content */}
        <div className="grid grid-cols-12 gap-6">
          {/* Sidebar */}
          <div className="col-span-12 md:col-span-3 lg:col-span-2">
            <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm h-full">
              <CardContent className="p-4">
                <nav className="space-y-2">
                  <NavItem
                    icon={Command}
                    label="Portfolio"
                    active={currentView === "portfolio"}
                    onClick={() => setCurrentView("portfolio")}
                  />
                  <NavItem
                    icon={Target}
                    label="Milestones"
                    active={currentView === "milestones"}
                    onClick={() => setCurrentView("milestones")}
                  />
                  <NavItem
                    icon={Users}
                    label="Teams"
                    active={currentView === "teams"}
                    onClick={() => setCurrentView("teams")}
                  />
                  <NavItem
                    icon={Github}
                    label="Repositories"
                    active={currentView === "repositories"}
                    onClick={() => setCurrentView("repositories")}
                  />
                  <NavItem
                    icon={Activity}
                    label="Analytics"
                    active={currentView === "analytics"}
                    onClick={() => setCurrentView("analytics")}
                  />
                  <NavItem
                    icon={AlertCircle}
                    label="Issues"
                    active={currentView === "issues"}
                    onClick={() => setCurrentView("issues")}
                  />
                  <NavItem
                    icon={MessageSquare}
                    label="Reviews"
                    active={currentView === "reviews"}
                    onClick={() => setCurrentView("reviews")}
                  />
                  <NavItem
                    icon={Settings}
                    label="Settings"
                    active={currentView === "settings"}
                    onClick={() => setCurrentView("settings")}
                  />
                </nav>

                <div className="mt-8 pt-6 border-t border-slate-700/50">
                  <div className="text-xs text-slate-500 mb-2 font-mono">PROJECT HEALTH</div>
                  <div className="space-y-3">
                    <StatusItem label="Portfolio" value={portfolioHealth} color="cyan" />
                    <StatusItem label="Milestones" value={milestoneProgress} color="green" />
                    <StatusItem label="Team Velocity" value={teamVelocity} color="blue" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main dashboard */}
          <div className="col-span-12 md:col-span-9 lg:col-span-7">{renderMainContent()}</div>

          {/* Right sidebar */}
          <div className="col-span-12 lg:col-span-3">
            <div className="grid gap-6">
              {/* Project timeline */}
              <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm overflow-hidden">
                <CardContent className="p-0">
                  <div className="bg-gradient-to-br from-slate-800 to-slate-900 p-6 border-b border-slate-700/50">
                    <div className="text-center">
                      <div className="text-xs text-slate-500 mb-1 font-mono">PROJECT TIMELINE</div>
                      <div className="text-3xl font-mono text-cyan-400 mb-1">{formatTime(currentTime)}</div>
                      <div className="text-sm text-slate-400">{formatDate(currentTime)}</div>
                    </div>
                  </div>
                  <div className="p-4">
                    <div className="grid grid-cols-2 gap-3">
                      <div className="bg-slate-800/50 rounded-md p-3 border border-slate-700/50">
                        <div className="text-xs text-slate-500 mb-1">Days Elapsed</div>
                        <div className="text-sm font-mono text-slate-200">{projectMetrics.daysElapsed} / 90</div>
                      </div>
                      <div className="bg-slate-800/50 rounded-md p-3 border border-slate-700/50">
                        <div className="text-xs text-slate-500 mb-1">Milestone</div>
                        <div className="text-sm font-mono text-slate-200">{projectMetrics.currentMilestone} of 6</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick actions */}
              <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
                <CardHeader className="pb-2">
                  <CardTitle className="text-slate-100 text-base">Quick Actions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-3">
                    <ActionButton icon={FileText} label="Generate Report" />
                    <ActionButton icon={RefreshCw} label="Sync Data" onClick={refetch} />
                    <ActionButton icon={Download} label="Export CSV" />
                    <ActionButton icon={Calendar} label="Schedule" />
                  </div>
                </CardContent>
              </Card>

              {/* Project controls */}
              <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
                <CardHeader className="pb-2">
                  <CardTitle className="text-slate-100 text-base">Project Controls</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Bell className="text-cyan-500 mr-2 h-4 w-4" />
                        <Label className="text-sm text-slate-400">Real-time Alerts</Label>
                      </div>
                      <Switch defaultChecked />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <RefreshCw className="text-cyan-500 mr-2 h-4 w-4" />
                        <Label className="text-sm text-slate-400">Auto Sync</Label>
                      </div>
                      <Switch defaultChecked />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Star className="text-cyan-500 mr-2 h-4 w-4" />
                        <Label className="text-sm text-slate-400">Priority Tracking</Label>
                      </div>
                      <Switch />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Zap className="text-cyan-500 mr-2 h-4 w-4" />
                        <Label className="text-sm text-slate-400">Performance Mode</Label>
                      </div>
                      <Switch defaultChecked />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}