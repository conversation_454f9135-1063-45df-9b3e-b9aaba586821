import React from 'react'

interface QualityHealthTrendsProps {
  projects: any[]
}

interface QualityDataPoint {
  week: string
  weekLabel: string
  bugRate: number // Percentage of bug-related issues vs total issues
  reviewCoverage: number // Percentage of PRs that had proper reviews
  deploymentSuccess: number // Percentage of successful deployments
  technicalDebt: number // Composite score of technical debt indicators
}

export function QualityHealthTrends({ projects }: QualityHealthTrendsProps) {
  // Generate quality metrics for the last 12 weeks
  const generateQualityData = (): QualityDataPoint[] => {
    const weeks: QualityDataPoint[] = []

    for (let i = 11; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - (i * 7))

      const weekStart = new Date(date)
      weekStart.setDate(date.getDate() - date.getDay())

      const weekEnd = new Date(weekStart)
      weekEnd.setDate(weekStart.getDate() + 6)

      const weekLabel = weekStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })

      // Calculate quality metrics for this week
      let totalIssues = 0
      let bugIssues = 0
      let totalPRs = 0
      let reviewedPRs = 0
      let totalWorkflowRuns = 0
      let successfulRuns = 0
      let stalePRs = 0
      let totalActivePRs = 0

      projects.forEach(project => {
        // Bug rate calculation
        const weekIssues = project.issues.filter((issue: any) => {
          const issueDate = new Date(issue.created_at)
          return issueDate >= weekStart && issueDate <= weekEnd
        })

        totalIssues += weekIssues.length
        bugIssues += weekIssues.filter((issue: any) =>
          issue.labels.some((label: any) =>
            label.name.toLowerCase().includes('bug') ||
            label.name.toLowerCase().includes('fix') ||
            label.name.toLowerCase().includes('error')
          )
        ).length

        // Review coverage calculation
        const weekPRs = project.pullRequests.filter((pr: any) => {
          const prDate = new Date(pr.created_at)
          return prDate >= weekStart && prDate <= weekEnd
        })

        totalPRs += weekPRs.length
        reviewedPRs += weekPRs.filter((pr: any) =>
          pr.requested_reviewers.length > 0 || pr.state === 'closed'
        ).length

        // Deployment success rate
        const weekRuns = project.workflowRuns?.filter((run: any) => {
          const runDate = new Date(run.created_at)
          return runDate >= weekStart && runDate <= weekEnd
        }) || []

        totalWorkflowRuns += weekRuns.length
        successfulRuns += weekRuns.filter((run: any) => run.conclusion === 'success').length

        // Technical debt indicators (stale PRs)
        const activePRs = project.pullRequests.filter((pr: any) => pr.state === 'open')
        totalActivePRs += activePRs.length

        stalePRs += activePRs.filter((pr: any) => {
          const daysSinceUpdate = (Date.now() - new Date(pr.updated_at).getTime()) / (1000 * 60 * 60 * 24)
          return daysSinceUpdate > 7
        }).length
      })

      // Calculate percentages and scores
      const bugRate = totalIssues > 0 ? (bugIssues / totalIssues) * 100 : 0
      const reviewCoverage = totalPRs > 0 ? (reviewedPRs / totalPRs) * 100 : 100
      const deploymentSuccess = totalWorkflowRuns > 0 ? (successfulRuns / totalWorkflowRuns) * 100 : 100

      // Technical debt score (inverted - lower stale PR ratio = better score)
      const stalePRRatio = totalActivePRs > 0 ? stalePRs / totalActivePRs : 0
      const technicalDebt = Math.max(0, 100 - (stalePRRatio * 100))

      weeks.push({
        week: weekStart.toISOString().split('T')[0],
        weekLabel,
        bugRate: Math.min(100, bugRate), // Cap at 100%
        reviewCoverage,
        deploymentSuccess,
        technicalDebt
      })
    }

    return weeks
  }

  const qualityData = generateQualityData()

  // Chart dimensions
  const chartWidth = 500
  const chartHeight = 220
  const padding = { top: 20, right: 20, bottom: 40, left: 40 }
  const innerWidth = chartWidth - padding.left - padding.right
  const innerHeight = chartHeight - padding.top - padding.bottom

  // Create scales (0-100 for all percentages)
  const xScale = (index: number) => (index / (qualityData.length - 1)) * innerWidth
  const yScale = (value: number) => innerHeight - (value / 100) * innerHeight

  // Generate paths for each metric
  const createPath = (data: number[]) => {
    return data.map((value, index) => {
      const x = xScale(index)
      const y = yScale(value)
      return index === 0 ? `M ${x} ${y}` : `L ${x} ${y}`
    }).join(' ')
  }

  // Invert bug rate for display (lower bugs = better quality)
  const bugRatePath = createPath(qualityData.map(d => 100 - d.bugRate))
  const reviewCoveragePath = createPath(qualityData.map(d => d.reviewCoverage))
  const deploymentSuccessPath = createPath(qualityData.map(d => d.deploymentSuccess))
  const technicalDebtPath = createPath(qualityData.map(d => d.technicalDebt))

  // Calculate overall health score
  const recentWeeks = qualityData.slice(-4)
  const overallHealthScore = recentWeeks.reduce((sum, d) => {
    return sum + ((100 - d.bugRate) + d.reviewCoverage + d.deploymentSuccess + d.technicalDebt) / 4
  }, 0) / recentWeeks.length

  const getHealthColor = (score: number) => {
    if (score >= 80) return 'text-green-400'
    if (score >= 60) return 'text-amber-400'
    return 'text-red-400'
  }

  const getHealthLabel = (score: number) => {
    if (score >= 80) return 'Excellent'
    if (score >= 60) return 'Good'
    return 'Needs Attention'
  }

  return (
    <div className="h-64 w-full relative bg-slate-800/20 rounded-lg border border-slate-700/30 p-4">
      {/* Header with health score */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4 text-xs">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
            <span className="text-slate-400">Code Quality</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
            <span className="text-slate-400">Review Coverage</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
            <span className="text-slate-400">Deploy Success</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-cyan-500 rounded-full mr-2"></div>
            <span className="text-slate-400">Tech Health</span>
          </div>
        </div>
        <div className="text-right">
          <div className="text-xs text-slate-500">Overall Health</div>
          <div className={`text-sm font-mono ${getHealthColor(overallHealthScore)}`}>
            {overallHealthScore.toFixed(1)}%
            <span className="ml-1 text-xs">
              {getHealthLabel(overallHealthScore)}
            </span>
          </div>
        </div>
      </div>

      {/* Chart */}
      <div className="relative">
        <svg width={chartWidth} height={chartHeight} className="overflow-visible">
          <defs>
            {/* Gradients for better visual appeal */}
            <linearGradient id="qualityGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="rgb(34, 197, 94)" stopOpacity="0.2" />
              <stop offset="100%" stopColor="rgb(34, 197, 94)" stopOpacity="0" />
            </linearGradient>
            <linearGradient id="reviewGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="rgb(59, 130, 246)" stopOpacity="0.2" />
              <stop offset="100%" stopColor="rgb(59, 130, 246)" stopOpacity="0" />
            </linearGradient>
            <linearGradient id="deployGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="rgb(147, 51, 234)" stopOpacity="0.2" />
              <stop offset="100%" stopColor="rgb(147, 51, 234)" stopOpacity="0" />
            </linearGradient>
            <linearGradient id="debtGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="rgb(6, 182, 212)" stopOpacity="0.2" />
              <stop offset="100%" stopColor="rgb(6, 182, 212)" stopOpacity="0" />
            </linearGradient>
          </defs>

          <g transform={`translate(${padding.left}, ${padding.top})`}>
            {/* Grid lines */}
            {[0, 25, 50, 75, 100].map(percent => {
              const y = innerHeight - (percent / 100) * innerHeight
              return (
                <g key={percent}>
                  <line
                    x1={0}
                    y1={y}
                    x2={innerWidth}
                    y2={y}
                    stroke="rgb(51, 65, 85)"
                    strokeWidth="1"
                    strokeOpacity="0.3"
                  />
                  <text
                    x={-10}
                    y={y + 4}
                    fill="rgb(148, 163, 184)"
                    fontSize="10"
                    textAnchor="end"
                  >
                    {percent}%
                  </text>
                </g>
              )
            })}

            {/* Area fills */}
            <path
              d={`${bugRatePath} L ${xScale(qualityData.length - 1)} ${innerHeight} L ${xScale(0)} ${innerHeight} Z`}
              fill="url(#qualityGradient)"
            />

            {/* Lines */}
            <path
              d={bugRatePath}
              fill="none"
              stroke="rgb(34, 197, 94)"
              strokeWidth="2"
              strokeLinecap="round"
              strokeDasharray="none"
            />
            <path
              d={reviewCoveragePath}
              fill="none"
              stroke="rgb(59, 130, 246)"
              strokeWidth="2"
              strokeLinecap="round"
            />
            <path
              d={deploymentSuccessPath}
              fill="none"
              stroke="rgb(147, 51, 234)"
              strokeWidth="2"
              strokeLinecap="round"
            />
            <path
              d={technicalDebtPath}
              fill="none"
              stroke="rgb(6, 182, 212)"
              strokeWidth="2"
              strokeLinecap="round"
              strokeDasharray="4,4"
            />

            {/* Data points */}
            {qualityData.map((d, index) => (
              <g key={index}>
                <circle
                  cx={xScale(index)}
                  cy={yScale(100 - d.bugRate)}
                  r="3"
                  fill="rgb(34, 197, 94)"
                  className="hover:r-4 transition-all cursor-pointer"
                />
                <circle
                  cx={xScale(index)}
                  cy={yScale(d.reviewCoverage)}
                  r="3"
                  fill="rgb(59, 130, 246)"
                  className="hover:r-4 transition-all cursor-pointer"
                />
                <circle
                  cx={xScale(index)}
                  cy={yScale(d.deploymentSuccess)}
                  r="3"
                  fill="rgb(147, 51, 234)"
                  className="hover:r-4 transition-all cursor-pointer"
                />
                <circle
                  cx={xScale(index)}
                  cy={yScale(d.technicalDebt)}
                  r="3"
                  fill="rgb(6, 182, 212)"
                  className="hover:r-4 transition-all cursor-pointer"
                />
              </g>
            ))}

            {/* X-axis labels */}
            {qualityData.map((d, index) => {
              if (index % 2 === 0) {
                return (
                  <text
                    key={index}
                    x={xScale(index)}
                    y={innerHeight + 15}
                    fill="rgb(148, 163, 184)"
                    fontSize="10"
                    textAnchor="middle"
                  >
                    {d.weekLabel}
                  </text>
                )
              }
              return null
            })}
          </g>
        </svg>

        {/* Hover tooltips */}
        <div className="absolute inset-0 flex">
          {qualityData.map((d, index) => (
            <div
              key={index}
              className="flex-1 relative group"
              style={{ minWidth: `${innerWidth / qualityData.length}px` }}
            >
              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 bg-slate-900/95 border border-slate-700/50 rounded-lg p-3 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10 whitespace-nowrap">
                <div className="text-xs font-medium text-slate-200 mb-2">{d.weekLabel}</div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                      <span className="text-slate-400">Code Quality:</span>
                    </div>
                    <span className="text-green-400 ml-2">{(100 - d.bugRate).toFixed(1)}%</span>
                  </div>
                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                      <span className="text-slate-400">Review Coverage:</span>
                    </div>
                    <span className="text-blue-400 ml-2">{d.reviewCoverage.toFixed(1)}%</span>
                  </div>
                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
                      <span className="text-slate-400">Deploy Success:</span>
                    </div>
                    <span className="text-purple-400 ml-2">{d.deploymentSuccess.toFixed(1)}%</span>
                  </div>
                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-cyan-500 rounded-full mr-2"></div>
                      <span className="text-slate-400">Tech Health:</span>
                    </div>
                    <span className="text-cyan-400 ml-2">{d.technicalDebt.toFixed(1)}%</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}