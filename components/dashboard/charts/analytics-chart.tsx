export function AnalyticsChart() {
  return (
    <div className="h-full w-full flex items-end justify-between px-4 pt-4 pb-8 relative">
      {/* Y-axis labels */}
      <div className="absolute left-2 top-0 h-full flex flex-col justify-between py-4">
        <div className="text-xs text-slate-500">1000</div>
        <div className="text-xs text-slate-500">750</div>
        <div className="text-xs text-slate-500">500</div>
        <div className="text-xs text-slate-500">250</div>
        <div className="text-xs text-slate-500">0</div>
      </div>

      {/* Chart bars */}
      <div className="flex-1 h-full flex items-end justify-between px-2 z-10">
        {Array.from({ length: 30 }).map((_, i) => {
          const height = Math.floor(Math.random() * 60) + 20

          return (
            <div key={i} className="flex space-x-0.5">
              <div
                className="w-1 bg-gradient-to-t from-cyan-500 to-cyan-400 rounded-t-sm"
                style={{ height: `${height}%` }}
              ></div>
            </div>
          )
        })}
      </div>
    </div>
  )
}