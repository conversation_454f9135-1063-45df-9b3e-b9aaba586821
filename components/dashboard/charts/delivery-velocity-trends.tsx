import React from 'react'

interface DeliveryVelocityTrendsProps {
  projects: any[]
}

interface VelocityDataPoint {
  date: string
  dateLabel: string
  commits: number
  mergedPRs: number
  closedIssues: number
  totalTasks: number
}

export function DeliveryVelocityTrends({ projects }: DeliveryVelocityTrendsProps) {
  // Generate velocity data starting from the project start date in 2-day intervals
  const generateVelocityData = (): VelocityDataPoint[] => {
    const dataPoints: VelocityDataPoint[] = []
    const projectStartDate = new Date('2025-05-02')
    projectStartDate.setHours(0, 0, 0, 0) // Normalize to start of the day

    const intervalDays = 2
    let currentIntervalStart = new Date(projectStartDate)

    const now = new Date()
    now.setHours(23, 59, 59, 999) // Normalize to end of the current day

    // Generate data as long as the current interval starts before or on today's end
    while (currentIntervalStart <= now) {
      const intervalStart = new Date(currentIntervalStart)

      const intervalEnd = new Date(intervalStart)
      intervalEnd.setDate(intervalStart.getDate() + intervalDays - 1)
      intervalEnd.setHours(23, 59, 59, 999) // Normalize to end of the interval

      // Ensure the interval does not go beyond the current date
      const actualIntervalEnd = intervalEnd > now ? now : intervalEnd

      const dateLabel = intervalStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })

      // Calculate metrics for this 2-day interval
      let commits = 0
      let mergedPRs = 0
      let closedIssues = 0

      // Only calculate metrics if the interval is after or includes the project start date
      if (actualIntervalEnd >= projectStartDate) {
        projects.forEach(project => {
          // Count commits in this interval
          commits += project.commits.filter((commit: any) => {
            const commitDate = new Date(commit.commit.author.date)
            return commitDate >= intervalStart && commitDate <= actualIntervalEnd
          }).length

          // Count merged PRs in this interval
          mergedPRs += project.pullRequests.filter((pr: any) => {
            if (!pr.merged_at) return false
            const mergeDate = new Date(pr.merged_at)
            return mergeDate >= intervalStart && mergeDate <= actualIntervalEnd
          }).length

          // Count closed issues in this interval
          closedIssues += project.issues.filter((issue: any) => {
            if (!issue.closed_at || issue.state !== 'closed') return false
            const closedDate = new Date(issue.closed_at)
            return closedDate >= intervalStart && closedDate <= actualIntervalEnd
          }).length
        })
      }

      dataPoints.push({
        date: intervalStart.toISOString().split('T')[0],
        dateLabel,
        commits,
        mergedPRs,
        closedIssues,
        totalTasks: commits + mergedPRs + closedIssues
      })

      // Move to the start of the next interval
      currentIntervalStart.setDate(currentIntervalStart.getDate() + intervalDays)
    }

    return dataPoints
  }

  const intervalDays = 2
  const velocityData = generateVelocityData()

  // Calculate chart dimensions and scales
  const chartWidth = 500
  const chartHeight = 220
  const padding = { top: 20, right: 20, bottom: 40, left: 40 }
  const innerWidth = chartWidth - padding.left - padding.right
  const innerHeight = chartHeight - padding.top - padding.bottom

  // Find max values for scaling
  const maxCommits = Math.max(...velocityData.map(d => d.commits), 10)
  const maxPRs = Math.max(...velocityData.map(d => d.mergedPRs), 5)
  const maxIssues = Math.max(...velocityData.map(d => d.closedIssues), 5)
  const maxTotal = Math.max(maxCommits, maxPRs, maxIssues, 10) // Ensure minimum height

  // Create scales
  const xScale = (index: number) => velocityData.length <= 1 ? 0 : (index / (velocityData.length - 1)) * innerWidth
  const yScale = (value: number) => innerHeight - (value / maxTotal) * innerHeight

  // Generate path for each metric
  const createPath = (data: number[]) => {
    if (data.length <= 1) return ''
    return data.map((value, index) => {
      const x = xScale(index)
      const y = yScale(value)
      return index === 0 ? `M ${x} ${y}` : `L ${x} ${y}`
    }).join(' ')
  }

  const commitsPath = createPath(velocityData.map(d => d.commits))
  const prsPath = createPath(velocityData.map(d => d.mergedPRs))
  const issuesPath = createPath(velocityData.map(d => d.closedIssues))

  // Calculate average and trend (using intervals instead of weeks)
  const activeIntervals = velocityData.filter(d => d.totalTasks > 0)
  // Use a similar duration for trend calculation, e.g., last 8 intervals (16 days) vs previous 8 intervals
  const trendIntervals = 8 // 8 intervals * 2 days/interval = 16 days
  const recentIntervals = activeIntervals.slice(-trendIntervals)
  const earlierIntervals = activeIntervals.slice(0, Math.min(trendIntervals, activeIntervals.length - recentIntervals.length)) // Ensure enough data for comparison and avoid overlap

  const recentAvg = recentIntervals.length > 0 ? recentIntervals.reduce((sum, d) => sum + d.totalTasks, 0) / recentIntervals.length : 0
  const earlierAvg = earlierIntervals.length > 0 ? earlierIntervals.reduce((sum, d) => sum + d.totalTasks, 0) / earlierIntervals.length : 0

  const trendPercentage = earlierAvg > 0 ? ((recentAvg - earlierAvg) / earlierAvg) * 100 : 0

  return (
    <div className="h-64 w-full relative bg-slate-800/20 rounded-lg border border-slate-700/30 p-4">
      {/* Header with stats */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4 text-xs">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-cyan-500 rounded-full mr-2"></div>
            <span className="text-slate-400">Commits</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
            <span className="text-slate-400">Merged PRs</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
            <span className="text-slate-400">Closed Issues</span>
          </div>
        </div>
        <div className="text-right">
          <div className="text-xs text-slate-500">Avg per {intervalDays} days</div>
          <div className={`text-sm font-mono ${trendPercentage >= 0 ? 'text-green-400' : 'text-red-400'}`}>
            {recentAvg.toFixed(1)}
            <span className="ml-1 text-xs">
              ({trendPercentage >= 0 ? '+' : ''}{trendPercentage.toFixed(1)}%)
            </span>
          </div>
        </div>
      </div>

      {/* Chart */}
      <div className="relative">
        <svg width={chartWidth} height={chartHeight} className="overflow-visible">
          <defs>
            {/* Gradients */}
            <linearGradient id="commitsGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="rgb(6, 182, 212)" stopOpacity="0.3" />
              <stop offset="100%" stopColor="rgb(6, 182, 212)" stopOpacity="0" />
            </linearGradient>
            <linearGradient id="prsGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="rgb(147, 51, 234)" stopOpacity="0.3" />
              <stop offset="100%" stopColor="rgb(147, 51, 234)" stopOpacity="0" />
            </linearGradient>
            <linearGradient id="issuesGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="rgb(59, 130, 246)" stopOpacity="0.3" />
              <stop offset="100%" stopColor="rgb(59, 130, 246)" stopOpacity="0" />
            </linearGradient>
          </defs>

          <g transform={`translate(${padding.left}, ${padding.top})`}>
            {/* Grid lines */}
            {[0, 25, 50, 75, 100].map(percent => {
              const y = innerHeight - (percent / 100) * innerHeight
              const value = Math.round((percent / 100) * maxTotal)
              // Only show grid lines for values if they are reasonably spaced
              if (maxTotal > 20 && value % 5 !== 0 && value !== 0) return null
              if (maxTotal <= 20 && value % 2 !== 0 && value !== 0) return null

              return (
                <g key={percent}>
                  <line
                    x1={0}
                    y1={y}
                    x2={innerWidth}
                    y2={y}
                    stroke="rgb(51, 65, 85)"
                    strokeWidth="1"
                    strokeOpacity="0.3"
                  />
                  <text
                    x={-10}
                    y={y + 4}
                    fill="rgb(148, 163, 184)"
                    fontSize="10"
                    textAnchor="end"
                  >
                    {value}
                  </text>
                </g>
              )
            })}

            {/* Area fills */}
            <path
              d={`${commitsPath} L ${xScale(velocityData.length - 1)} ${innerHeight} L ${xScale(0)} ${innerHeight} Z`}
              fill="url(#commitsGradient)"
            />
            <path
              d={`${prsPath} L ${xScale(velocityData.length - 1)} ${innerHeight} L ${xScale(0)} ${innerHeight} Z`}
              fill="url(#prsGradient)"
            />
            <path
              d={`${issuesPath} L ${xScale(velocityData.length - 1)} ${innerHeight} L ${xScale(0)} ${innerHeight} Z`}
              fill="url(#issuesGradient)"
            />

            {/* Lines */}
            <path
              d={commitsPath}
              fill="none"
              stroke="rgb(6, 182, 212)"
              strokeWidth="2"
              strokeLinecap="round"
            />
            <path
              d={prsPath}
              fill="none"
              stroke="rgb(147, 51, 234)"
              strokeWidth="2"
              strokeLinecap="round"
            />
            <path
              d={issuesPath}
              fill="none"
              stroke="rgb(59, 130, 246)"
              strokeWidth="2"
              strokeLinecap="round"
            />

            {/* Data points */}
            {velocityData.map((d, index) => (
              <g key={index}>
                <circle
                  cx={xScale(index)}
                  cy={yScale(d.commits)}
                  r="3"
                  fill="rgb(6, 182, 212)"
                  className="hover:r-4 transition-all cursor-pointer"
                />
                <circle
                  cx={xScale(index)}
                  cy={yScale(d.mergedPRs)}
                  r="3"
                  fill="rgb(147, 51, 234)"
                  className="hover:r-4 transition-all cursor-pointer"
                />
                <circle
                  cx={xScale(index)}
                  cy={yScale(d.closedIssues)}
                  r="3"
                  fill="rgb(59, 130, 246)"
                  className="hover:r-4 transition-all cursor-pointer"
                />
              </g>
            ))}

            {/* X-axis labels */}
            {velocityData.map((d, index) => {
              // Show label for every 4th interval (every 8 days) and the last label
              if (index % 4 === 0 || index === velocityData.length - 1) {
                return (
                  <text
                    key={index}
                    x={xScale(index)}
                    y={innerHeight + 15}
                    fill="rgb(148, 163, 184)"
                    fontSize="10"
                    textAnchor="middle"
                  >
                    {d.dateLabel}
                  </text>
                )
              }
              return null
            })}
          </g>
        </svg>

        {/* Hover tooltip area */}
        <div className="absolute inset-0 flex">
          {velocityData.map((d, index) => (
            <div
              key={index}
              className="flex-1 relative group"
              style={{ minWidth: `${innerWidth / velocityData.length}px` }}
            >
              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 bg-slate-900/90 border border-slate-700/50 rounded-lg p-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10 whitespace-nowrap">
                <div className="text-xs font-medium text-slate-200 mb-1">{d.dateLabel}</div>
                <div className="space-y-1">
                  <div className="flex items-center text-xs">
                    <div className="w-2 h-2 bg-cyan-500 rounded-full mr-2"></div>
                    <span className="text-slate-400">Commits:</span>
                    <span className="text-cyan-400 ml-1">{d.commits}</span>
                  </div>
                  <div className="flex items-center text-xs">
                    <div className="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
                    <span className="text-slate-400">PRs:</span>
                    <span className="text-purple-400 ml-1">{d.mergedPRs}</span>
                  </div>
                  <div className="flex items-center text-xs">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                    <span className="text-slate-400">Issues:</span>
                    <span className="text-blue-400 ml-1">{d.closedIssues}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}