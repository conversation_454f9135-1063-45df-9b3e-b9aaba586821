import React from 'react';

interface BurndownBurnupChartProps {
  projects: any[]; // Or a more specific type if available
}

interface ChartDataPoint {
  date: string;
  dateLabel: string;
  totalScope: number; // Total issues ever relevant to the scope
  completed: number; // Issues completed by this date
  remaining: number; // Total scope - completed
  idealRemaining: number; // Ideal burndown line
}

export function BurndownBurnupChart({ projects }: BurndownBurnupChartProps) {
  const projectStartDate = new Date('2025-05-02');
  projectStartDate.setHours(0, 0, 0, 0);

  // Determine the end date for the chart (e.g., last milestone due date or current date)
  const now = new Date();
  now.setHours(23, 59, 59, 999);

  let chartEndDate = new Date(now);

  const allIssues = projects.reduce((acc, project) => acc.concat(project.issues || []), []);

  // Calculate initial scope (open issues at project start)
  const initialScope = allIssues.filter((issue: any) => {
    const createdDate = new Date(issue.created_at);
    return createdDate <= projectStartDate && issue.state === 'open';
  }).length;

  // Find the latest milestone due date after the project start
  const latestMilestoneDate = projects.reduce((latest: Date | null, project: any) => {
    project.milestones?.forEach((milestone: any) => {
      if (milestone && milestone.due_on) {
        const dueDate = new Date(milestone.due_on);
        if (dueDate > projectStartDate && (!latest || dueDate > latest)) {
          latest = dueDate;
        }
      }
    });
    return latest;
  }, null);

  // Calculate ideal burndown rate per day (assuming ideal linear progress to the latest milestone)
  let idealRate = 0;
  let idealBurndownEndDate = latestMilestoneDate || chartEndDate; // Use latest milestone if available, otherwise chart end
  if (idealBurndownEndDate > projectStartDate && initialScope > 0) {
    const totalDays = Math.ceil((idealBurndownEndDate.getTime() - projectStartDate.getTime()) / (1000 * 60 * 60 * 24));
    idealRate = initialScope / totalDays; // Ideal issues to close per day
  }

  if (latestMilestoneDate && latestMilestoneDate > chartEndDate) {
    chartEndDate = new Date(latestMilestoneDate);
    chartEndDate.setDate(chartEndDate.getDate() + 7); // Extend a bit beyond the last milestone
  }

  // Generate daily data points from project start to chart end date
  const generateChartData = (): ChartDataPoint[] => {
    const dataPoints: ChartDataPoint[] = [];
    let currentDate = new Date(projectStartDate);

    while (currentDate <= chartEndDate) {
      const dateLabel = currentDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });

      // Calculate total scope up to this date (initial scope + new issues created)
      const totalScope = initialScope + allIssues.filter((issue: any) => {
         const createdDate = new Date(issue.created_at);
         return createdDate > projectStartDate && createdDate <= currentDate;
       }).length;

      // Calculate completed issues up to this date
      const completed = allIssues.filter((issue: any) => {
        const closedDate = issue.closed_at ? new Date(issue.closed_at) : null;
        return closedDate && closedDate <= currentDate && issue.state === 'closed';
      }).length;

      const remaining = totalScope - completed;

       // Calculate ideal remaining for burndown
       const daysPassed = Math.ceil((currentDate.getTime() - projectStartDate.getTime()) / (1000 * 60 * 60 * 24));
       const idealRemaining = Math.max(0, initialScope - (idealRate * daysPassed));

      dataPoints.push({
        date: currentDate.toISOString().split('T')[0],
        dateLabel,
        totalScope,
        completed,
        remaining,
        idealRemaining
      });

      // Move to the next day
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return dataPoints;
  };

  const chartData = generateChartData();

   if (chartData.length === 0) {
     return (
       <div className="h-64 w-full flex items-center justify-center bg-slate-800/20 rounded-lg border border-slate-700/30 p-4">
         <p className="text-slate-400">Not enough data to display burndown/burnup chart yet.</p>
       </div>
     );
   }

  // Chart dimensions and scales (similar to velocity chart, adjusting maxTotal)
  const chartWidth = 500;
  const chartHeight = 220;
  const padding = { top: 20, right: 20, bottom: 40, left: 40 };
  const innerWidth = chartWidth - padding.left - padding.right;
  const innerHeight = chartHeight - padding.top - padding.bottom;

  // Find max value for scaling (based on total scope)
  const maxTotalScope = Math.max(...chartData.map(d => d.totalScope), 10); // Ensure minimum height

  // Create scales
  const xScale = (index: number) => chartData.length <= 1 ? 0 : (index / (chartData.length - 1)) * innerWidth;
  const yScale = (value: number) => innerHeight - (value / maxTotalScope) * innerHeight;

  // Generate paths
  const createPath = (data: number[]) => {
     if (data.length <= 1) return '';
    return data.map((value, index) => {
      const x = xScale(index);
      const y = yScale(value);
      return index === 0 ? `M ${x} ${y}` : `L ${x} ${y}`;
    }).join(' ');
  };

  const totalScopePath = createPath(chartData.map(d => d.totalScope));
  const completedPath = createPath(chartData.map(d => d.completed)); // Burnup
  const remainingPath = createPath(chartData.map(d => d.remaining)); // Burndown
   const idealRemainingPath = createPath(chartData.map(d => d.idealRemaining)); // Ideal Burndown

  // Find the latest data point for current values
  const latestData = chartData[chartData.length - 1];

  return (
    <div className="h-64 w-full relative bg-slate-800/20 rounded-lg border border-slate-700/30 p-4">
      {/* Header with stats */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4 text-xs">
          <div className="flex items-center">
             <div className="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
             <span className="text-slate-400">Total Scope</span>
           </div>
           <div className="flex items-center">
             <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
             <span className="text-slate-400">Completed (Burnup)</span>
           </div>
           <div className="flex items-center">
             <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
             <span className="text-slate-400">Remaining (Burndown)</span>
           </div>
            {latestMilestoneDate && (
              <div className="flex items-center">
                <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                <span className="text-slate-400">Ideal Burndown</span>
              </div>
            )}
        </div>
        <div className="text-right">
          <div className="text-xs text-slate-500">Current Status</div>
          <div className="text-sm font-mono text-slate-200">
             Scope: {latestData.totalScope}, Done: {latestData.completed}, To Do: {latestData.remaining}
          </div>
        </div>
      </div>

      {/* Chart */}
      <div className="relative">
        <svg width={chartWidth} height={chartHeight} className="overflow-visible">
          <defs>
            {/* Gradients */}
             <linearGradient id="scopeGradient" x1="0%" y1="0%" x2="0%" y2="100%">
               <stop offset="0%" stopColor="rgb(253, 186, 116)" stopOpacity="0.3" />
               <stop offset="100%" stopColor="rgb(253, 186, 116)" stopOpacity="0" />
             </linearGradient>
            <linearGradient id="completedGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="rgb(34, 197, 94)" stopOpacity="0.3" />
              <stop offset="100%" stopColor="rgb(34, 197, 94)" stopOpacity="0" />
            </linearGradient>
            <linearGradient id="remainingGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="rgb(239, 68, 68)" stopOpacity="0.3" />
              <stop offset="100%" stopColor="rgb(239, 68, 68)" stopOpacity="0" />
            </linearGradient>
          </defs>

          <g transform={`translate(${padding.left}, ${padding.top})`}>
            {/* Grid lines */}
            {[0, 25, 50, 75, 100].map(percent => {
              const y = innerHeight - (percent / 100) * innerHeight;
              const value = Math.round((percent / 100) * maxTotalScope);
               // Only show grid lines for values if they are reasonably spaced
              if (maxTotalScope > 20 && value % 5 !== 0 && value !== 0) return null;
              if (maxTotalScope <= 20 && value % 2 !== 0 && value !== 0) return null;

              return (
                <g key={percent}>
                  <line
                    x1={0}
                    y1={y}
                    x2={innerWidth}
                    y2={y}
                    stroke="rgb(51, 65, 85)"
                    strokeWidth="1"
                    strokeOpacity="0.3"
                  />
                  <text
                    x={-10}
                    y={y + 4}
                    fill="rgb(148, 163, 184)"
                    fontSize="10"
                    textAnchor="end"
                  >
                    {value}
                  </text>
                </g>
              );
            })}

            {/* Area fills */}
             <path
               d={`${totalScopePath} L ${xScale(chartData.length - 1)} ${innerHeight} L ${xScale(0)} ${innerHeight} Z`}
               fill="url(#scopeGradient)"
             />
             <path
               d={`${completedPath} L ${xScale(chartData.length - 1)} ${innerHeight} L ${xScale(0)} ${innerHeight} Z`}
               fill="url(#completedGradient)"
             />
            {/* Note: No fill for remaining as it's a burndown line */}

            {/* Lines */}
             <path
               d={totalScopePath}
               fill="none"
               stroke="rgb(253, 186, 116)"
               strokeWidth="2"
               strokeLinecap="round"
               strokeDasharray="4,4"
             />
            <path
              d={completedPath}
              fill="none"
              stroke="rgb(34, 197, 94)"
              strokeWidth="2"
              strokeLinecap="round"
            />
            <path
              d={remainingPath}
              fill="none"
              stroke="rgb(239, 68, 68)"
              strokeWidth="2"
              strokeLinecap="round"
            />
             {latestMilestoneDate && initialScope > 0 && idealBurndownEndDate > projectStartDate && (
               <path
                 d={idealRemainingPath}
                 fill="none"
                 stroke="rgb(59, 130, 246)"
                 strokeWidth="2"
                 strokeLinecap="round"
                 strokeDasharray="2,2"
               />
             )}

            {/* Data points */}
            {chartData.map((d, index) => (
              <g key={index}>
                 <circle
                   cx={xScale(index)}
                   cy={yScale(d.totalScope)}
                   r="3"
                   fill="rgb(253, 186, 116)"
                   className="hover:r-4 transition-all cursor-pointer"
                 />
                <circle
                  cx={xScale(index)}
                  cy={yScale(d.completed)}
                  r="3"
                  fill="rgb(34, 197, 94)"
                  className="hover:r-4 transition-all cursor-pointer"
                />
                <circle
                  cx={xScale(index)}
                  cy={yScale(d.remaining)}
                  r="3"
                  fill="rgb(239, 68, 68)"
                  className="hover:r-4 transition-all cursor-pointer"
                />
              </g>
            ))}

            {/* X-axis labels */}
            {chartData.map((d, index) => {
              // Show label for every 7th day (weekly) and the first/last label
              if (index % 7 === 0 || index === chartData.length - 1 || index === 0) {
                return (
                  <text
                    key={index}
                    x={xScale(index)}
                    y={innerHeight + 15}
                    fill="rgb(148, 163, 184)"
                    fontSize="10"
                    textAnchor="middle"
                  >
                    {d.dateLabel}
                  </text>
                );
              }
              return null;
            })}

             {/* Y-axis label */}
             <text
               x={-padding.left}
               y={innerHeight / 2}
               transform={`rotate(-90, -${padding.left}, ${innerHeight / 2})`}
               fill="rgb(148, 163, 184)"
               fontSize="12"
               textAnchor="middle"
             >
               Number of Issues
             </text>
          </g>
        </svg>

        {/* Hover tooltip area */}
        <div className="absolute inset-0 flex">
          {chartData.map((d, index) => (
            <div
              key={index}
              className="flex-1 relative group"
              style={{ minWidth: `${innerWidth / chartData.length}px` }}
            >
              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 bg-slate-900/90 border border-slate-700/50 rounded-lg p-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10 whitespace-nowrap">
                <div className="text-xs font-medium text-slate-200 mb-1">{d.dateLabel}</div>
                <div className="space-y-1">
                   <div className="flex items-center text-xs">
                    <div className="w-2 h-2 bg-orange-500 rounded-full mr-2"></div>
                    <span className="text-slate-400">Scope:</span>
                    <span className="text-orange-400 ml-1">{d.totalScope}</span>
                  </div>
                  <div className="flex items-center text-xs">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-slate-400">Completed:</span>
                    <span className="text-green-400 ml-1">{d.completed}</span>
                  </div>
                  <div className="flex items-center text-xs">
                    <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                    <span className="text-slate-400">Remaining:</span>
                    <span className="text-red-400 ml-1">{d.remaining}</span>
                  </div>
                   {latestMilestoneDate && (
                     <div className="flex items-center text-xs">
                       <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                       <span className="text-slate-400">Ideal To Do:</span>
                       <span className="text-blue-400 ml-1">{Math.round(d.idealRemaining)}</span>
                     </div>
                   )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}