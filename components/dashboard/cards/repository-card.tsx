import { Badge } from "@/components/ui/badge"
import { Star, GitBranch } from "lucide-react"

interface RepositoryCardProps {
  name: string
  description: string
  language: string
  stars: number
  forks: number
  issues: number
  prs: number
  lastCommit: string
  status: "active" | "behind" | "critical"
}

export function RepositoryCard({
  name,
  description,
  language,
  stars,
  forks,
  issues,
  prs,
  lastCommit,
  status,
}: RepositoryCardProps) {
  const getStatusBadge = () => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-500/20 text-green-400 border-green-500/50 text-xs">Active</Badge>
      case "behind":
        return <Badge className="bg-amber-500/20 text-amber-400 border-amber-500/50 text-xs">Behind</Badge>
      case "critical":
        return <Badge className="bg-red-500/20 text-red-400 border-red-500/50 text-xs">Critical</Badge>
      default:
        return null
    }
  }

  return (
    <div className="flex items-center justify-between p-4 bg-slate-800/50 rounded-md border border-slate-700/50 hover:bg-slate-800/70">
      <div className="flex-1">
        <div className="flex items-center space-x-2 mb-1">
          <h3 className="text-sm font-medium text-slate-200">{name}</h3>
          {getStatusBadge()}
        </div>
        <p className="text-xs text-slate-400 mb-2">{description}</p>
        <div className="flex items-center space-x-4 text-xs text-slate-500">
          <span className="flex items-center">
            <div className="w-2 h-2 rounded-full bg-blue-500 mr-1"></div>
            {language}
          </span>
          <span className="flex items-center">
            <Star className="w-3 h-3 mr-1" />
            {stars}
          </span>
          <span className="flex items-center">
            <GitBranch className="w-3 h-3 mr-1" />
            {forks}
          </span>
          <span>{issues} issues</span>
          <span>{prs} PRs</span>
          <span>Updated {lastCommit}</span>
        </div>
      </div>
    </div>
  )
} 