import { TrendingUp, LineChart } from "lucide-react"

interface IssueMetricProps {
  title: string
  count: number
  trend: "up" | "down" | "stable"
  color: string
}

export function IssueMetric({ title, count, trend, color }: IssueMetricProps) {
  const getTrendIcon = () => {
    switch (trend) {
      case "up":
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case "down":
        return <TrendingUp className="h-4 w-4 rotate-180 text-red-500" />
      case "stable":
        return <LineChart className="h-4 w-4 text-blue-500" />
      default:
        return null
    }
  }

  const getColorClass = () => {
    switch (color) {
      case "green":
        return "text-green-400"
      case "amber":
        return "text-amber-400"
      case "blue":
        return "text-blue-400"
      case "red":
        return "text-red-400"
      case "purple":
        return "text-purple-400"
      default:
        return "text-slate-400"
    }
  }

  return (
    <div className="bg-slate-800/50 rounded-md p-3 border border-slate-700/50">
      <div className="flex items-center justify-between mb-2">
        <div className="text-sm text-slate-400">{title}</div>
        {getTrendIcon()}
      </div>
      <div className={`text-2xl font-bold ${getColorClass()}`}>{count}</div>
    </div>
  )
}