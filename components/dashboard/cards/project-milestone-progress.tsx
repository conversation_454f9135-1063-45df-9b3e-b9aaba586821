interface ProjectMilestoneProgressProps {
  project: string
  milestone: number
  progress: number
}

export function ProjectMilestoneProgress({ project, milestone, progress }: ProjectMilestoneProgressProps) {
  return (
    <div className="flex items-center justify-between p-3 bg-slate-800/50 rounded-md border border-slate-700/50">
      <div>
        <div className="text-sm font-medium text-slate-200">{project}</div>
        <div className="text-xs text-slate-400">Milestone {milestone}</div>
      </div>
      <div className="flex items-center space-x-2">
        <div className="w-24 h-2 bg-slate-700 rounded-full overflow-hidden">
          <div
            className="h-full bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        <span className="text-xs text-cyan-400 w-8">{progress}%</span>
      </div>
    </div>
  )
}