import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Target } from "lucide-react"

interface MilestoneProgressCardProps {
  milestoneProgress: number
  idealMilestoneProgress: number
  selectedMilestone?: string
  variant?: "full" | "compact"
}

export function MilestoneProgressCard({
  milestoneProgress,
  idealMilestoneProgress,
  selectedMilestone = "all",
  variant = "full"
}: MilestoneProgressCardProps) {
  if (variant === "compact") {
    return <CompactMilestoneProgressCard
      milestoneProgress={milestoneProgress}
      idealMilestoneProgress={idealMilestoneProgress}
      selectedMilestone={selectedMilestone}
    />;
  }

  // Use dynamic target calculation
  const actualTarget = selectedMilestone === "all" ? idealMilestoneProgress : 100;
  const actualDifference = milestoneProgress - actualTarget;
  const actualIsAhead = actualDifference > 0;
  const actualIsBehind = actualDifference < 0;
  const actualIsOnTrack = Math.abs(actualDifference) <= 5;

  const getStatusColor = () => {
    if (actualIsOnTrack) return "text-green-400";
    if (actualIsAhead) return "text-blue-400";
    return "text-amber-400";
  };

  const getStatusText = () => {
    if (actualIsOnTrack) return "On Track";
    if (actualIsAhead) return `${Math.abs(actualDifference)}% Ahead`;
    return `${Math.abs(actualDifference)}% Behind`;
  };

  const getDisplayText = () => {
    if (selectedMilestone === "all") return "All Milestones (6 total)";
    return selectedMilestone;
  };

  // Calculate current milestone based on target (for display purposes)
  const currentMilestoneNumber = selectedMilestone === "all"
    ? Math.round((idealMilestoneProgress / 100) * 6)
    : parseInt(selectedMilestone.match(/\d+/)?.[0] || "1");

  return (
    <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-base font-medium text-slate-100">
          Milestone Progress
        </CardTitle>
        <Target className="h-4 w-4 text-cyan-500" />
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {/* Actual Progress */}
          <div>
            <div className="text-2xl font-bold text-slate-100">
              {milestoneProgress}%
            </div>
            <p className="text-xs text-slate-400">Current Progress</p>
          </div>

          {/* Progress Comparison Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-xs text-slate-400">
              <span>Actual</span>
              <span>Target: {Math.round(actualTarget)}%</span>
            </div>
            <div className="relative h-2 bg-slate-800 rounded-full overflow-hidden">
              {/* Target line */}
              <div
                className="absolute top-0 w-0.5 h-full bg-slate-400 z-10"
                style={{ left: `${Math.min(actualTarget, 100)}%` }}
              />
              {/* Actual progress bar */}
              <div
                className={`h-full rounded-full transition-all duration-300 ${
                  actualIsOnTrack
                    ? "bg-gradient-to-r from-green-500 to-emerald-500"
                    : actualIsAhead
                    ? "bg-gradient-to-r from-blue-500 to-cyan-500"
                    : "bg-gradient-to-r from-amber-500 to-orange-500"
                }`}
                style={{ width: `${Math.min(milestoneProgress, 100)}%` }}
              />
            </div>
          </div>

          {/* Status and Details */}
          <div className="flex items-center justify-between">
            <div className="text-xs text-slate-500">
              {getDisplayText()}
            </div>
            <Badge
              variant="outline"
              className={`text-xs ${getStatusColor()} border-current bg-current/10`}
            >
              {getStatusText()}
            </Badge>
          </div>

          {/* Milestone Breakdown */}
          <div className="pt-2 border-t border-slate-700/50">
            <div className="grid grid-cols-6 gap-1">
              {Array.from({ length: 6 }, (_, i) => {
                const milestoneNum = i + 1;
                const expectedComplete = milestoneNum <= currentMilestoneNumber;
                return (
                  <div
                    key={milestoneNum}
                    className={`h-1.5 rounded-sm ${
                      expectedComplete
                        ? "bg-slate-600"
                        : "bg-slate-800"
                    }`}
                    title={`Milestone ${milestoneNum}`}
                  />
                );
              })}
            </div>
            <div className="flex justify-between text-xs text-slate-500 mt-1">
              <span>M1</span>
              <span>M6</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function CompactMilestoneProgressCard({
  milestoneProgress,
  idealMilestoneProgress,
  selectedMilestone = "all"
}: {
  milestoneProgress: number;
  idealMilestoneProgress: number;
  selectedMilestone?: string;
}) {
  // Use dynamic target calculation
  const actualTarget = selectedMilestone === "all" ? idealMilestoneProgress : 100;
  const difference = milestoneProgress - actualTarget;

  const getStatusColor = () => {
    if (Math.abs(difference) <= 5) return "text-green-400";
    if (difference > 0) return "text-blue-400";
    return "text-amber-400";
  };

  return (
    <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-base font-medium text-slate-100">
          Milestone Progress
        </CardTitle>
        <Target className="h-4 w-4 text-cyan-500" />
      </CardHeader>
      <CardContent>
        <div className="flex items-baseline space-x-2">
          <div className="text-2xl font-bold text-slate-100">
            {milestoneProgress}%
          </div>
          <div className="text-sm text-slate-400">
            / {Math.round(actualTarget)}% target
          </div>
          <Badge
            variant="outline"
            className={`text-xs ml-auto ${getStatusColor()} border-current bg-current/10`}
          >
            {difference > 0 ? '+' : ''}{Math.round(difference)}%
          </Badge>
        </div>

        <div className="mt-2">
          <div className="relative h-2 bg-slate-800 rounded-full overflow-hidden">
            <div
              className="absolute top-0 w-0.5 h-full bg-slate-400"
              style={{ left: `${Math.min(actualTarget, 100)}%` }}
            />
            <div
              className="h-full bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full"
              style={{ width: `${Math.min(milestoneProgress, 100)}%` }}
            />
          </div>
        </div>

        <p className="text-xs text-slate-500 mt-2">
          {selectedMilestone === "all" ? "All Milestones" : selectedMilestone}
        </p>
      </CardContent>
    </Card>
  );
}