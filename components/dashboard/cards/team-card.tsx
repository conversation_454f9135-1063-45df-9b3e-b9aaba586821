import { Badge } from "@/components/ui/badge"

interface TeamCardProps {
  name: string
  members: number
  lead: string
  projects: string[]
  velocity: number
}

export function TeamCard({ name, members, lead, projects, velocity }: TeamCardProps) {
  return (
    <div className="bg-slate-800/50 rounded-lg border border-slate-700/50 p-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-medium text-slate-200">{name}</h3>
        <Badge variant="outline" className="text-xs bg-slate-700/50 text-slate-300 border-slate-600/50">
          {members} members
        </Badge>
      </div>
      <div className="space-y-2">
        <div className="flex items-center justify-between text-sm">
          <span className="text-slate-400">Team Lead:</span>
          <span className="text-slate-200">{lead}</span>
        </div>
        <div className="flex items-center justify-between text-sm">
          <span className="text-slate-400">Projects:</span>
          <span className="text-slate-200">{projects.join(", ")}</span>
        </div>
        <div className="flex items-center justify-between text-sm">
          <span className="text-slate-400">Velocity:</span>
          <span className="text-cyan-400">{velocity}%</span>
        </div>
      </div>
    </div>
  )
}