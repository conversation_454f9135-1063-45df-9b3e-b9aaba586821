import { Badge } from "@/components/ui/badge"
import { Activity, GitCommit, GitPullRequest, GitMerge, Eye, AlertCircle } from "lucide-react"

interface ActivityItemProps {
  type: "commit" | "pr" | "issue" | "review" | "merge"
  user: string
  action: string
  target: string
  time: string
  project: string
}

export function ActivityItem({ type, user, action, target, time, project }: ActivityItemProps) {
  const getTypeIcon = () => {
    switch (type) {
      case "commit":
        return <GitCommit className="h-4 w-4 text-green-500" />
      case "pr":
        return <GitPullRequest className="h-4 w-4 text-blue-500" />
      case "issue":
        return <AlertCircle className="h-4 w-4 text-amber-500" />
      case "review":
        return <Eye className="h-4 w-4 text-purple-500" />
      case "merge":
        return <GitMerge className="h-4 w-4 text-cyan-500" />
      default:
        return <Activity className="h-4 w-4 text-slate-500" />
    }
  }

  return (
    <div className="flex space-x-3 p-2 rounded-md hover:bg-slate-800/50">
      <div className="flex-shrink-0 mt-0.5">{getTypeIcon()}</div>
      <div className="flex-1">
        <div className="text-sm text-slate-200">
          <span className="font-medium">{user}</span> {action} <span className="text-cyan-400">{target}</span>
        </div>
        <div className="flex items-center justify-between mt-1">
          <Badge variant="outline" className="text-xs bg-slate-800/50 text-slate-400 border-slate-600/50">
            {project}
          </Badge>
          <div className="text-xs text-slate-500">{time}</div>
        </div>
      </div>
    </div>
  )
}