import { Badge } from "@/components/ui/badge"

interface ProjectRowProps {
  name: string
  progress: number
  issues: { open: number; closed: number }
  prs: { open: number; merged: number }
  team: number
  status: "on-track" | "at-risk" | "behind"
}

export function ProjectRow({ name, progress, issues, prs, team, status }: ProjectRowProps) {
  const getStatusBadge = () => {
    switch (status) {
      case "on-track":
        return <Badge className="bg-green-500/20 text-green-400 border-green-500/50 text-xs">On Track</Badge>
      case "at-risk":
        return <Badge className="bg-amber-500/20 text-amber-400 border-amber-500/50 text-xs">At Risk</Badge>
      case "behind":
        return <Badge className="bg-red-500/20 text-red-400 border-red-500/50 text-xs">Behind</Badge>
      default:
        return null
    }
  }

  return (
    <div className="grid grid-cols-12 py-3 px-3 text-sm hover:bg-slate-800/50">
      <div className="col-span-3 text-slate-300 font-medium">{name}</div>
      <div className="col-span-2">
        <div className="flex items-center space-x-2">
          <div className="w-12 h-1.5 bg-slate-700 rounded-full overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
          <span className="text-xs text-cyan-400">{progress}%</span>
        </div>
      </div>
      <div className="col-span-2 text-slate-400">
        <span className="text-amber-400">{issues.open}</span> / <span className="text-green-400">{issues.closed}</span>
      </div>
      <div className="col-span-2 text-slate-400">
        <span className="text-blue-400">{prs.open}</span> / <span className="text-green-400">{prs.merged}</span>
      </div>
      <div className="col-span-2 text-slate-400">{team} devs</div>
      <div className="col-span-1">{getStatusBadge()}</div>
    </div>
  )
}