import { Switch } from "@/components/ui/switch"

interface SettingToggleProps {
  label: string
  description: string
  defaultChecked: boolean
}

export function SettingToggle({ label, description, defaultChecked }: SettingToggleProps) {
  return (
    <div className="flex items-center justify-between p-3 bg-slate-800/50 rounded-md border border-slate-700/50">
      <div>
        <div className="text-sm font-medium text-slate-200">{label}</div>
        <div className="text-xs text-slate-400">{description}</div>
      </div>
      <Switch defaultChecked={defaultChecked} />
    </div>
  )
}