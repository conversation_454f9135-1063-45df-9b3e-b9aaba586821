import { Badge } from "@/components/ui/badge"
import { AlertCircle } from "lucide-react"

interface AlertItemProps {
  title: string
  time: string
  description: string
  type: "info" | "warning" | "error" | "success"
  project: string
}

export function AlertItem({ title, time, description, type, project }: AlertItemProps) {
  const getTypeStyles = () => {
    switch (type) {
      case "info":
        return { color: "text-blue-500 bg-blue-500/10 border-blue-500/30" }
      case "warning":
        return { color: "text-amber-500 bg-amber-500/10 border-amber-500/30" }
      case "error":
        return { color: "text-red-500 bg-red-500/10 border-red-500/30" }
      case "success":
        return { color: "text-green-500 bg-green-500/10 border-green-500/30" }
      default:
        return { color: "text-blue-500 bg-blue-500/10 border-blue-500/30" }
    }
  }

  const { color } = getTypeStyles()

  return (
    <div className="flex items-start space-x-3">
      <div className={`mt-0.5 p-1 rounded-full ${color.split(" ")[1]} ${color.split(" ")[2]}`}>
        <AlertCircle className={`h-3 w-3 ${color.split(" ")[0]}`} />
      </div>
      <div className="flex-1">
        <div className="flex items-center justify-between">
          <div className="text-sm font-medium text-slate-200">{title}</div>
          <div className="text-xs text-slate-500">{time}</div>
        </div>
        <div className="text-xs text-slate-400">{description}</div>
        <Badge variant="outline" className="mt-1 text-xs bg-slate-800/50 text-slate-400 border-slate-600/50">
          {project}
        </Badge>
      </div>
    </div>
  )
}