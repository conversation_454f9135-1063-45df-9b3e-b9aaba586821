interface PerformanceMetricProps {
  label: string
  value: number
  color: string
}

export function PerformanceMetric({ label, value, color }: PerformanceMetricProps) {
  const getColorClass = () => {
    switch (color) {
      case "green":
        return "from-green-500 to-green-400"
      case "blue":
        return "from-blue-500 to-blue-400"
      case "purple":
        return "from-purple-500 to-purple-400"
      case "amber":
        return "from-amber-500 to-amber-400"
      default:
        return "from-slate-500 to-slate-400"
    }
  }

  return (
    <div className="flex items-center justify-between">
      <div className="text-sm text-slate-400">{label}</div>
      <div className="flex items-center space-x-2">
        <div className="w-24 h-2 bg-slate-700 rounded-full overflow-hidden">
          <div
            className={`h-full bg-gradient-to-r ${getColorClass()} rounded-full`}
            style={{ width: `${value}%` }}
          ></div>
        </div>
        <span className="text-xs text-slate-400 w-8">{value}%</span>
      </div>
    </div>
  )
}