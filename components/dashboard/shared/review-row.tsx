import { Badge } from "@/components/ui/badge"

interface ReviewRowProps {
  pr: string
  title: string
  author: string
  reviewer: string
  project: string
  status: "pending" | "approved" | "changes-requested"
  created: string
  changes: string
}

export function ReviewRow({ pr, title, author, reviewer, project, status, created, changes }: ReviewRowProps) {
  const getStatusBadge = () => {
    switch (status) {
      case "pending":
        return <Badge className="bg-amber-500/20 text-amber-400 border-amber-500/50 text-xs">Pending</Badge>
      case "approved":
        return <Badge className="bg-green-500/20 text-green-400 border-green-500/50 text-xs">Approved</Badge>
      case "changes-requested":
        return <Badge className="bg-red-500/20 text-red-400 border-red-500/50 text-xs">Changes Requested</Badge>
      default:
        return null
    }
  }

  return (
    <div className="flex items-center justify-between p-3 bg-slate-800/50 rounded-md border border-slate-700/50 hover:bg-slate-800/70">
      <div className="flex-1">
        <div className="flex items-center space-x-2 mb-1">
          <span className="text-sm text-slate-500">{pr}</span>
          <h3 className="text-sm font-medium text-slate-200">{title}</h3>
        </div>
        <div className="flex items-center space-x-4 text-xs text-slate-400">
          <span>
            {author} → {reviewer}
          </span>
          <span>{project}</span>
          <span>{changes}</span>
          <span>Created {created}</span>
        </div>
      </div>
      <div className="flex items-center space-x-2">{getStatusBadge()}</div>
    </div>
  )
}