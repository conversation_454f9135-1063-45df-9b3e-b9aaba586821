import { Button } from "@/components/ui/button"
import { type LucideIcon } from "lucide-react"

interface NavItemProps {
  icon: LucideIcon
  label: string
  active?: boolean
  onClick: () => void
}

export function NavItem({ icon: Icon, label, active, onClick }: NavItemProps) {
  return (
    <Button
      variant="ghost"
      onClick={onClick}
      className={`w-full justify-start ${active ? "bg-slate-800/70 text-cyan-400" : "text-slate-400 hover:text-slate-100"}`}
    >
      <Icon className="mr-2 h-4 w-4" />
      {label}
    </Button>
  )
}