import { Badge } from "@/components/ui/badge"

interface MilestoneItemProps {
  number: number
  title: string
  status: "completed" | "active" | "upcoming"
  date: string
  progress: number
  description: string
}

export function MilestoneItem({ number, title, status, date, progress, description }: MilestoneItemProps) {
  const getStatusColor = () => {
    switch (status) {
      case "completed":
        return "bg-green-500"
      case "active":
        return "bg-cyan-500"
      case "upcoming":
        return "bg-slate-600"
      default:
        return "bg-slate-600"
    }
  }

  return (
    <div className="relative flex items-start space-x-4 pb-8">
      <div
        className={`flex-shrink-0 w-8 h-8 rounded-full ${getStatusColor()} flex items-center justify-center text-white text-sm font-bold z-10`}
      >
        {number}
      </div>
      <div className="flex-1">
        <div className="flex items-center justify-between mb-1">
          <h4 className="text-lg font-medium text-slate-200">{title}</h4>
          <Badge variant="outline" className="text-xs bg-slate-800/50 text-slate-400 border-slate-600/50">
            {date}
          </Badge>
        </div>
        <p className="text-sm text-slate-400 mb-2">{description}</p>
        {status !== "upcoming" && (
          <div className="flex items-center space-x-2">
            <div className="w-32 h-2 bg-slate-700 rounded-full overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <span className="text-xs text-cyan-400">{progress}%</span>
          </div>
        )}
      </div>
    </div>
  )
}