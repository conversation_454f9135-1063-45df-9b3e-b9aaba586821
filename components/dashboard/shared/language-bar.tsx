interface LanguageBarProps {
  language: string
  percentage: number
  color: string
}

export function LanguageBar({ language, percentage, color }: LanguageBarProps) {
  const getColorClass = () => {
    switch (color) {
      case "blue":
        return "from-blue-500 to-blue-400"
      case "cyan":
        return "from-cyan-500 to-cyan-400"
      case "green":
        return "from-green-500 to-green-400"
      case "yellow":
        return "from-yellow-500 to-yellow-400"
      case "gray":
        return "from-gray-500 to-gray-400"
      default:
        return "from-slate-500 to-slate-400"
    }
  }

  return (
    <div className="flex items-center justify-between">
      <div className="text-sm text-slate-400">{language}</div>
      <div className="flex items-center space-x-2">
        <div className="w-24 h-2 bg-slate-700 rounded-full overflow-hidden">
          <div
            className={`h-full bg-gradient-to-r ${getColorClass()} rounded-full`}
            style={{ width: `${percentage}%` }}
          ></div>
        </div>
        <span className="text-xs text-slate-400 w-8">{percentage}%</span>
      </div>
    </div>
  )
}