import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"

interface RealTeamMemberProps {
  name: string
  role: string
  commits: number
  reviews: number
  velocity: number
  avatar: string
  projects: string[]
}

export function RealTeamMember({ name, role, commits, reviews, velocity, avatar, projects }: RealTeamMemberProps) {
  return (
    <div className="flex items-center space-x-3">
      <Avatar className="h-8 w-8">
        <AvatarImage src={avatar || "/placeholder.svg"} alt={name} />
        <AvatarFallback className="bg-slate-700 text-cyan-500">{name.slice(0, 2).toUpperCase()}</AvatarFallback>
      </Avatar>
      <div className="flex-1">
        <div className="flex items-center justify-between">
          <div className="text-sm font-medium text-slate-200">{name}</div>
          <div className="text-xs text-cyan-400">{commits + reviews}</div>
        </div>
        <div className="flex items-center justify-between">
          <div className="text-xs text-slate-500">{role}</div>
          <div className="text-xs text-slate-400">
            {commits} commits • {reviews} reviews
          </div>
        </div>
        <div className="text-xs text-slate-500 mt-1">
          Projects: {projects.length > 0 ? projects.join(", ") : "None"}
        </div>
      </div>
    </div>
  )
}

interface DetailedTeamMemberProps {
  name: string
  role: string
  team: string
  commits: number
  reviews: number
  velocity: number
  projects: string[]
  avatar: string
}

export function DetailedTeamMember({ name, role, team, commits, reviews, velocity, projects, avatar }: DetailedTeamMemberProps) {
  return (
    <div className="flex items-center space-x-4 p-3 bg-slate-800/50 rounded-md border border-slate-700/50">
      <Avatar className="h-10 w-10">
        <AvatarImage src={avatar || "/placeholder.svg"} alt={name} />
        <AvatarFallback className="bg-slate-700 text-cyan-500">
          {name
            .split(" ")
            .map((n) => n[0])
            .join("")}
        </AvatarFallback>
      </Avatar>
      <div className="flex-1">
        <div className="flex items-center justify-between">
          <div>
            <div className="text-sm font-medium text-slate-200">{name}</div>
            <div className="text-xs text-slate-400">
              {role} • {team}
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-cyan-400">{commits + reviews}</div>
            <div className="text-xs text-slate-400"></div>
          </div>
        </div>
        <div className="flex items-center justify-between mt-2">
          <div className="text-xs text-slate-400">
            {commits} commits • {reviews} reviews
          </div>
          <div className="text-xs text-slate-400">{projects.join(", ")}</div>
        </div>
      </div>
    </div>
  )
}