import { Badge } from "@/components/ui/badge"

interface IssueRowProps {
  id: string
  title: string
  project: string
  assignee: string
  priority: "high" | "medium" | "low"
  status: "open" | "in-progress" | "in-review" | "blocked"
  created: string
}

export function IssueRow({ id, title, project, assignee, priority, status, created }: IssueRowProps) {
  const getPriorityBadge = () => {
    switch (priority) {
      case "high":
        return <Badge className="bg-red-500/20 text-red-400 border-red-500/50 text-xs">High</Badge>
      case "medium":
        return <Badge className="bg-amber-500/20 text-amber-400 border-amber-500/50 text-xs">Medium</Badge>
      case "low":
        return <Badge className="bg-green-500/20 text-green-400 border-green-500/50 text-xs">Low</Badge>
      default:
        return null
    }
  }

  const getStatusBadge = () => {
    switch (status) {
      case "open":
        return (
          <Badge variant="outline" className="bg-slate-800/50 text-slate-400 border-slate-600/50 text-xs">
            Open
          </Badge>
        )
      case "in-progress":
        return <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/50 text-xs">In Progress</Badge>
      case "in-review":
        return <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/50 text-xs">In Review</Badge>
      case "blocked":
        return <Badge className="bg-red-500/20 text-red-400 border-red-500/50 text-xs">Blocked</Badge>
      default:
        return null
    }
  }

  return (
    <div className="flex items-center justify-between p-3 bg-slate-800/50 rounded-md border border-slate-700/50 hover:bg-slate-800/70">
      <div className="flex-1">
        <div className="flex items-center space-x-2 mb-1">
          <span className="text-sm text-slate-500">{id}</span>
          <h3 className="text-sm font-medium text-slate-200">{title}</h3>
        </div>
        <div className="flex items-center space-x-4 text-xs text-slate-400">
          <span>{project}</span>
          <span>Assigned to {assignee}</span>
          <span>Created {created}</span>
        </div>
      </div>
      <div className="flex items-center space-x-2">
        {getPriorityBadge()}
        {getStatusBadge()}
      </div>
    </div>
  )
}