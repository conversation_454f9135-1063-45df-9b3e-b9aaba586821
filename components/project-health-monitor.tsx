"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { AlertTriangle, CheckCircle, Clock, GitBranch } from "lucide-react"

interface ProjectHealthMonitorProps {
  projectsData: any[]
}

export function ProjectHealthMonitor({ projectsData }: ProjectHealthMonitorProps) {
  const getHealthScore = (project: any) => {
    const openIssues = project.issues.filter((i: any) => i.state === "open").length
    const totalIssues = project.issues.length
    const openPRs = project.pullRequests.filter((pr: any) => pr.state === "open").length

    let score = 100

    // Deduct points for high issue count
    if (openIssues > 20) score -= 30
    else if (openIssues > 10) score -= 15
    else if (openIssues > 5) score -= 5

    // Deduct points for stale PRs
    if (openPRs > 5) score -= 20
    else if (openPRs > 3) score -= 10

    // Deduct points for low completion rate
    const completionRate = totalIssues > 0 ? ((totalIssues - openIssues) / totalIssues) * 100 : 100
    if (completionRate < 50) score -= 25
    else if (completionRate < 70) score -= 15

    return Math.max(0, score)
  }

  const getHealthStatus = (score: number) => {
    if (score >= 80) return { status: "healthy", color: "green", icon: CheckCircle }
    if (score >= 60) return { status: "warning", color: "amber", icon: Clock }
    return { status: "critical", color: "red", icon: AlertTriangle }
  }

  return (
    <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
      <CardHeader className="pb-2">
        <CardTitle className="text-slate-100 flex items-center text-base">
          <GitBranch className="mr-2 h-5 w-5 text-cyan-500" />
          Project Health Monitor
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {projectsData.map((project) => {
            const healthScore = getHealthScore(project)
            const { status, color, icon: Icon } = getHealthStatus(healthScore)

            return (
              <div key={project.name} className="p-3 bg-slate-800/50 rounded-md border border-slate-700/50">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <h3 className="text-sm font-medium text-slate-200 capitalize">{project.name}</h3>
                    <Icon className={`h-4 w-4 text-${color}-500`} />
                  </div>
                  <Badge className={`bg-${color}-500/20 text-${color}-400 border-${color}-500/50 text-xs`}>
                    {healthScore}%
                  </Badge>
                </div>

                <Progress value={healthScore} className="h-2 mb-2" />

                <div className="grid grid-cols-3 gap-2 text-xs">
                  <div className="text-center">
                    <div className="text-slate-400">Open Issues</div>
                    <div className="text-amber-400 font-medium">
                      {project.issues.filter((i: any) => i.state === "open").length}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-slate-400">Open PRs</div>
                    <div className="text-blue-400 font-medium">
                      {project.pullRequests.filter((pr: any) => pr.state === "open").length}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-slate-400">Progress</div>
                    <div className="text-cyan-400 font-medium">{project.progress}%</div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
