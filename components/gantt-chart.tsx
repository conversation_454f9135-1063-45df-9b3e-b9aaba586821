"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar, Target } from "lucide-react"

interface Milestone {
  id: number
  title: string
  description?: string | null
  state: "open" | "closed"
  created_at: string
  updated_at: string
  due_on?: string | null
  projectName?: string
}

interface GanttChartProps {
  milestones: Milestone[]
}

export function GanttChart({ milestones = [] }: GanttChartProps) {
  // Sort milestones by due date
  const sortedMilestones = milestones
    .filter((m) => m.due_on)
    .sort((a, b) => new Date(a.due_on!).getTime() - new Date(b.due_on!).getTime())

  const getStatusColor = (milestone: Milestone) => {
    if (milestone.state === "closed") return "bg-green-500"

    const now = new Date()
    const dueDate = milestone.due_on ? new Date(milestone.due_on) : null

    if (!dueDate) return "bg-slate-500"
    if (dueDate < now) return "bg-red-500"
    if (dueDate <= new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)) return "bg-amber-500"

    return "bg-cyan-500"
  }

  const getStatusBadge = (milestone: Milestone) => {
    if (milestone.state === "closed") {
      return <Badge className="bg-green-500/20 text-green-400 border-green-500/50 text-xs">Completed</Badge>
    }

    const now = new Date()
    const dueDate = milestone.due_on ? new Date(milestone.due_on) : null

    if (!dueDate) {
      return <Badge className="bg-slate-500/20 text-slate-400 border-slate-500/50 text-xs">No Due Date</Badge>
    }

    if (dueDate < now) {
      return <Badge className="bg-red-500/20 text-red-400 border-red-500/50 text-xs">Overdue</Badge>
    }

    if (dueDate <= new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)) {
      return <Badge className="bg-amber-500/20 text-amber-400 border-amber-500/50 text-xs">Due Soon</Badge>
    }

    return <Badge className="bg-cyan-500/20 text-cyan-400 border-cyan-500/50 text-xs">On Track</Badge>
  }

  if (milestones.length === 0) {
    return (
      <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-slate-100 flex items-center">
            <Target className="mr-2 h-5 w-5 text-cyan-500" />
            Project Timeline
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-slate-400 py-8">
            <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No milestones found</p>
            <p className="text-sm mt-2">Create milestones in your GitHub repositories to see the timeline</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="text-slate-100 flex items-center">
          <Target className="mr-2 h-5 w-5 text-cyan-500" />
          Project Timeline
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {sortedMilestones.map((milestone, index) => (
            <div
              key={milestone.id}
              className="relative flex items-center space-x-4 p-4 bg-slate-800/50 rounded-lg border border-slate-700/50"
            >
              {/* Timeline indicator */}
              <div className="flex-shrink-0 relative">
                <div className={`w-4 h-4 rounded-full ${getStatusColor(milestone)} border-2 border-slate-900`}></div>
                {index < sortedMilestones.length - 1 && (
                  <div className="absolute top-4 left-1/2 transform -translate-x-1/2 w-0.5 h-8 bg-slate-700"></div>
                )}
              </div>

              {/* Milestone content */}
              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-lg font-medium text-slate-200">{milestone.title}</h3>
                  {getStatusBadge(milestone)}
                </div>

                {milestone.description && <p className="text-sm text-slate-400 mb-2">{milestone.description}</p>}

                <div className="flex items-center space-x-4 text-xs text-slate-500">
                  {milestone.projectName && (
                    <span className="bg-slate-700/50 px-2 py-1 rounded">
                      {milestone.projectName.charAt(0).toUpperCase() + milestone.projectName.slice(1)}
                    </span>
                  )}
                  {milestone.due_on && (
                    <span className="flex items-center">
                      <Calendar className="w-3 h-3 mr-1" />
                      Due: {new Date(milestone.due_on).toLocaleDateString()}
                    </span>
                  )}
                  <span>Created: {new Date(milestone.created_at).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
