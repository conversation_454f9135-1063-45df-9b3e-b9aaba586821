"use client"

import { useEffect, useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Activity, GitCommit, GitPullRequest, AlertCircle } from "lucide-react"

interface RealtimeMetricsProps {
  projectsData: any[]
}

export function RealtimeMetrics({ projectsData }: RealtimeMetricsProps) {
  const [recentActivity, setRecentActivity] = useState<any[]>([])

  useEffect(() => {
    // Process recent activity from all projects
    const allActivity: any[] = []

    projectsData.forEach((project) => {
      // Add recent commits
      project.commits.slice(0, 3).forEach((commit: any) => {
        allActivity.push({
          type: "commit",
          project: project.name,
          user: commit.author?.login || commit.commit.author.name,
          message: commit.commit.message.split("\n")[0],
          timestamp: new Date(commit.commit.author.date),
          avatar: commit.author?.avatar_url,
        })
      })

      // Add recent PRs
      project.pullRequests.slice(0, 2).forEach((pr: any) => {
        allActivity.push({
          type: "pr",
          project: project.name,
          user: pr.user.login,
          message: pr.title,
          timestamp: new Date(pr.created_at),
          avatar: pr.user.avatar_url,
          number: pr.number,
        })
      })

      // Add recent issues
      project.issues.slice(0, 2).forEach((issue: any) => {
        allActivity.push({
          type: "issue",
          project: project.name,
          user: issue.assignee?.login || "Unassigned",
          message: issue.title,
          timestamp: new Date(issue.created_at),
          avatar: issue.assignee?.avatar_url,
          number: issue.number,
          state: issue.state,
        })
      })
    })

    // Sort by timestamp and take most recent
    const sortedActivity = allActivity.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()).slice(0, 10)

    setRecentActivity(sortedActivity)
  }, [projectsData])

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "commit":
        return <GitCommit className="h-4 w-4 text-green-500" />
      case "pr":
        return <GitPullRequest className="h-4 w-4 text-blue-500" />
      case "issue":
        return <AlertCircle className="h-4 w-4 text-amber-500" />
      default:
        return <Activity className="h-4 w-4 text-slate-500" />
    }
  }

  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date()
    const diffMs = now.getTime() - timestamp.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMs / 3600000)
    const diffDays = Math.floor(diffMs / 86400000)

    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    return `${diffDays}d ago`
  }

  return (
    <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
      <CardHeader className="pb-2">
        <CardTitle className="text-slate-100 flex items-center text-base">
          <Activity className="mr-2 h-5 w-5 text-blue-500" />
          Live Activity Feed
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {recentActivity.map((activity, index) => (
            <div key={index} className="flex space-x-3 p-2 rounded-md hover:bg-slate-800/50">
              <div className="flex-shrink-0 mt-0.5">{getActivityIcon(activity.type)}</div>
              <div className="flex-1 min-w-0">
                <div className="text-sm text-slate-200">
                  <span className="font-medium">{activity.user}</span>
                  {activity.type === "commit" && " committed to "}
                  {activity.type === "pr" && " opened PR "}
                  {activity.type === "issue" && " created issue "}
                  <span className="text-cyan-400">
                    {activity.project}
                    {activity.number && `#${activity.number}`}
                  </span>
                </div>
                <div className="text-xs text-slate-400 truncate">{activity.message}</div>
                <div className="flex items-center justify-between mt-1">
                  <Badge variant="outline" className="text-xs bg-slate-800/50 text-slate-400 border-slate-600/50">
                    {activity.project}
                  </Badge>
                  <div className="text-xs text-slate-500">{formatTimeAgo(activity.timestamp)}</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
