"use client"

import { useState, useEffect } from "react"
import { fetchEnhancedTeamData } from "@/lib/github-api-enhanced"

interface DashboardData {
  portfolioHealth: number
  milestoneProgress: number
  idealMilestoneProgress: number
  teamVelocity: number
  issueResolution: number
  projectsData: any[]
  teamMembers: any[]
  totalCommits: number
  totalIssues: number
  totalPRs: number
  totalMergedPRs: number
  totalClosedIssues: number
  isLoading: boolean
  error: string | null
  lastUpdated: Date | null
}

export function useGitHubData() {
  const [data, setData] = useState<DashboardData>({
    portfolioHealth: 0,
    milestoneProgress: 0,
    idealMilestoneProgress: 0,
    teamVelocity: 0,
    issueResolution: 0,
    projectsData: [],
    teamMembers: [],
    totalCommits: 0,
    totalIssues: 0,
    totalPRs: 0,
    totalMergedPRs: 0,
    totalClosedIssues: 0,
    isLoading: true,
    error: null,
    lastUpdated: null,
  })

  const fetchData = async () => {
    try {
      setData((prev) => ({ ...prev, isLoading: true, error: null }))

      // Fetch projects data from our API route
      const projectsResponse = await fetch("/api/github/projects")
      if (!projectsResponse.ok) {
        const errorData = await projectsResponse.json()
        throw new Error(errorData.error || "Failed to fetch projects data")
      }
      const projectsData = await projectsResponse.json()

      // Fetch team members from our API route
      const teamResponse = await fetch("/api/github/team")
      const teamMembers = teamResponse.ok ? await teamResponse.json() : []

      setData({
        ...projectsData,
        teamMembers,
        isLoading: false,
        error: null,
        lastUpdated: new Date(),
      })
    } catch (error) {
      console.error("Error fetching GitHub data:", error)
      setData((prev) => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : "Failed to fetch data",
      }))
    }
  }

  useEffect(() => {
    fetchData()

    // Set up auto-refresh every 10 minutes for comprehensive data
    const interval = setInterval(fetchData, 10 * 60 * 1000)

    return () => clearInterval(interval)
  }, [])

  return {
    ...data,
    refetch: fetchData,
  }
}

// Hook for analytics data
export function useGitHubAnalytics() {
  const [data, setData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchAnalytics = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch("/api/github/analytics")
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to fetch analytics data")
      }

      const analyticsData = await response.json()
      setData(analyticsData)
    } catch (error) {
      console.error("Error fetching analytics:", error)
      setError(error instanceof Error ? error.message : "Failed to fetch analytics")
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchAnalytics()
  }, [])

  return {
    data,
    isLoading,
    error,
    refetch: fetchAnalytics,
  }
}

// Hook for milestones data
export function useGitHubMilestones() {
  const [data, setData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchMilestones = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch("/api/github/milestones")
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to fetch milestones data")
      }

      const milestonesData = await response.json()
      setData(milestonesData)
    } catch (error) {
      console.error("Error fetching milestones:", error)
      setError(error instanceof Error ? error.message : "Failed to fetch milestones")
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchMilestones()
  }, [])

  return {
    data,
    isLoading,
    error,
    refetch: fetchMilestones,
  }
}

// Add this new hook after the existing hooks
export function useEnhancedTeamData() {
  const [data, setData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchData = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch("/api/github/team")
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to fetch team data")
      }

      const teamData = await response.json()
      setData(teamData)
    } catch (error) {
      console.error("Error fetching enhanced team data:", error)
      setError(error instanceof Error ? error.message : "Failed to fetch team data")
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  return {
    data,
    isLoading,
    error,
    refetch: fetchData,
  }
}
