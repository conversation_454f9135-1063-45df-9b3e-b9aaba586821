import { NextResponse } from "next/server"
import { fetchAllProjectsData } from "@/lib/github-api-enhanced"

export async function GET() {
  try {
    const projectsData = await fetchAllProjectsData()

    // Aggregate analytics across all projects
    const totalCommits = projectsData.reduce((sum, project) => sum + project.analytics.totalCommits, 0)
    const totalIssues = projectsData.reduce((sum, project) => sum + project.analytics.totalIssues, 0)
    const totalPRs = projectsData.reduce((sum, project) => sum + project.analytics.totalPRs, 0)

    // Aggregate language statistics
    const languageStats = projectsData.reduce(
      (acc, project) => {
        Object.entries(project.analytics.languageStats).forEach(([lang, bytes]) => {
          acc[lang] = (acc[lang] || 0) + bytes
        })
        return acc
      },
      {} as Record<string, number>,
    )

    // Aggregate workflow statistics
    const workflowStats = {
      totalWorkflows: projectsData.reduce((sum, project) => sum + project.workflows.length, 0),
      totalRuns: projectsData.reduce((sum, project) => sum + project.workflowRuns.length, 0),
      successfulRuns: projectsData.reduce(
        (sum, project) => sum + project.workflowRuns.filter((run) => run.conclusion === "success").length,
        0,
      ),
      failedRuns: projectsData.reduce(
        (sum, project) => sum + project.workflowRuns.filter((run) => run.conclusion === "failure").length,
        0,
      ),
    }

    // Aggregate contributor statistics
    const contributorStats = projectsData.reduce(
      (acc, project) => {
        project.analytics.contributorStats.forEach((contributor) => {
          if (acc[contributor.author]) {
            acc[contributor.author].commits += contributor.commits
            acc[contributor.author].additions += contributor.additions
            acc[contributor.author].deletions += contributor.deletions
          } else {
            acc[contributor.author] = { ...contributor }
          }
        })
        return acc
      },
      {} as Record<string, any>,
    )

    return NextResponse.json({
      totalCommits,
      totalIssues,
      totalPRs,
      languageStats,
      workflowStats,
      contributorStats: Object.values(contributorStats),
      projectsData,
    })
  } catch (error) {
    console.error("Error fetching analytics:", error)
    return NextResponse.json(
      {
        error: "Failed to fetch analytics",
        totalCommits: 0,
        totalIssues: 0,
        totalPRs: 0,
        languageStats: {},
        workflowStats: {
          totalWorkflows: 0,
          totalRuns: 0,
          successfulRuns: 0,
          failedRuns: 0,
        },
        contributorStats: [],
        projectsData: [],
      },
      { status: 500 },
    )
  }
}
