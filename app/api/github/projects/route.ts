import { NextResponse } from "next/server"
import { fetchDashboardMetrics } from "@/lib/github-api-enhanced"

export async function GET() {
  try {
    if (!process.env.GITHUB_TOKEN) {
      return NextResponse.json({ error: "GitHub token not configured" }, { status: 500 })
    }

    console.log("Fetching comprehensive GitHub data...")
    const dashboardMetrics = await fetchDashboardMetrics()
    console.log("Successfully fetched all GitHub data")

    return NextResponse.json(dashboardMetrics)
  } catch (error) {
    console.error("Error in GitHub API route:", error)
    return NextResponse.json(
      {
        error: "Failed to fetch GitHub data",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    )
  }
}
