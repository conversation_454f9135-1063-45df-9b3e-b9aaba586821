import { NextResponse } from "next/server"
import { fetchEnhancedTeamData } from "@/lib/github-api-enhanced"

export async function GET() {
  try {
    if (!process.env.GITHUB_TOKEN) {
      return NextResponse.json({ error: "GitHub token not configured" }, { status: 500 })
    }

    const teamData = await fetchEnhancedTeamData()

    if (!teamData || Object.keys(teamData).length === 0) {
      return NextResponse.json({ error: "No team data available" }, { status: 404 })
    }

    return NextResponse.json(teamData)
  } catch (error) {
    console.error("Error fetching team data:", error)
    return NextResponse.json({ error: "Failed to fetch team data" }, { status: 500 })
  }
}
