import { NextResponse } from "next/server"
import { fetchAllMilestones } from "@/lib/github-api-enhanced"

export async function GET() {
  try {
    if (!process.env.GITHUB_TOKEN) {
      return NextResponse.json({ error: "GitHub token not configured" }, { status: 500 })
    }

    const milestonesData = await fetchAllMilestones()
    return NextResponse.json(milestonesData)
  } catch (error) {
    console.error("Error fetching milestones:", error)
    return NextResponse.json({
      milestones: [],
      stats: { total: 0, open: 0, closed: 0, overdue: 0 },
      projectsData: [],
    })
  }
}
