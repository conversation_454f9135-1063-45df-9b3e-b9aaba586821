// Enhanced GitHub API with comprehensive data fetching
import { github<PERSON>ache, CACHE_KEYS } from "./github-cache"

const GITHUB_TOKEN = process.env.GITHUB_TOKEN
const GITHUB_ORG = process.env.GITHUB_ORG || "berrijam"

export const PROJECTS = {
  strawberri: { projectId: 5, repo: "strawberri" },
  gojiberri: { projectId: 4, repo: "gojiberri" },
  mulberri: { projectId: 6, repo: "mulberri" },
  cranberri: { projectId: 8, repo: "cranberri" },
  elderberri: { projectId: 9, repo: "elderberri" },
}

// Enhanced interfaces
export interface GitHubRepo {
  id: number
  name: string
  full_name: string
  description: string | null
  language: string | null
  stargazers_count: number
  forks_count: number
  open_issues_count: number
  updated_at: string
  created_at: string
  pushed_at: string
  default_branch: string
  size: number
  topics: string[]
  visibility: string
  archived: boolean
  disabled: boolean
}

export interface GitHubIssue {
  id: number
  number: number
  title: string
  body: string | null
  state: "open" | "closed"
  created_at: string
  updated_at: string
  closed_at: string | null
  assignee?: {
    login: string
    avatar_url: string
    id: number
  }
  assignees: Array<{
    login: string
    avatar_url: string
    id: number
  }>
  labels: Array<{
    name: string
    color: string
    description: string | null
  }>
  milestone?: {
    id: number
    title: string
    description: string | null
    state: "open" | "closed"
    created_at: string
    updated_at: string
    due_on: string | null
  }
  user: {
    login: string
    avatar_url: string
    id: number
  }
  pull_request?: {
    url: string
  }
}

export interface GitHubPullRequest {
  id: number
  number: number
  title: string
  body: string | null
  state: "open" | "closed"
  created_at: string
  updated_at: string
  closed_at: string | null
  merged_at: string | null
  user: {
    login: string
    avatar_url: string
    id: number
  }
  assignee?: {
    login: string
    avatar_url: string
    id: number
  }
  assignees: Array<{
    login: string
    avatar_url: string
    id: number
  }>
  requested_reviewers: Array<{
    login: string
    avatar_url: string
    id: number
  }>
  labels: Array<{
    name: string
    color: string
  }>
  milestone?: {
    id: number
    title: string
    state: "open" | "closed"
    due_on: string | null
  }
  head: {
    ref: string
    sha: string
  }
  base: {
    ref: string
    sha: string
  }
  additions: number
  deletions: number
  changed_files: number
  mergeable: boolean | null
  draft: boolean
}

export interface GitHubCommit {
  sha: string
  commit: {
    message: string
    author: {
      name: string
      email: string
      date: string
    }
    committer: {
      name: string
      email: string
      date: string
    }
    tree: {
      sha: string
    }
    verification: {
      verified: boolean
      reason: string
    }
  }
  author?: {
    login: string
    avatar_url: string
    id: number
  }
  committer?: {
    login: string
    avatar_url: string
    id: number
  }
  stats?: {
    additions: number
    deletions: number
    total: number
  }
  files?: Array<{
    filename: string
    status: string
    additions: number
    deletions: number
    changes: number
  }>
}

export interface GitHubMember {
  login: string
  id: number
  avatar_url: string
  type: string
  site_admin: boolean
}

export interface GitHubWorkflow {
  id: number
  name: string
  path: string
  state: "active" | "deleted"
  created_at: string
  updated_at: string
  badge_url: string
}

export interface GitHubWorkflowRun {
  id: number
  name: string
  status: "queued" | "in_progress" | "completed"
  conclusion: "success" | "failure" | "neutral" | "cancelled" | "skipped" | "timed_out" | null
  created_at: string
  updated_at: string
  run_started_at: string
  workflow_id: number
  head_branch: string
  head_sha: string
  event: string
  actor: {
    login: string
    avatar_url: string
  }
}

export interface ProjectData {
  name: string
  repo: GitHubRepo
  issues: GitHubIssue[]
  pullRequests: GitHubPullRequest[]
  commits: GitHubCommit[]
  workflows: GitHubWorkflow[]
  workflowRuns: GitHubWorkflowRun[]
  contributors: GitHubMember[]
  progress: number
  status: "on-track" | "at-risk" | "behind"
  milestones: GitHubIssue["milestone"][]
  analytics: {
    totalCommits: number
    totalIssues: number
    totalPRs: number
    codeFrequency: Array<{ week: string; additions: number; deletions: number }>
    languageStats: Record<string, number>
    contributorStats: Array<{ author: string; commits: number; additions: number; deletions: number }>
  }
}

// REPLACE the existing githubFetch function:
const githubFetch = async (endpoint: string, options: RequestInit = {}, retries = 3) => {
  for (let i = 0; i < retries; i++) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch(`https://api.github.com${endpoint}`, {
        headers: {
          Authorization: `Bearer ${GITHUB_TOKEN}`,
          Accept: "application/vnd.github.v3+json",
          "X-GitHub-Api-Version": "2022-11-28",
          ...options.headers,
        },
        signal: controller.signal,
        ...options,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        console.error(`GitHub API error: ${response.status} ${response.statusText} for ${endpoint}`);
        throw new Error(`GitHub API error: ${response.status} ${response.statusText}`);
      }

      return response.json();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.warn(`Attempt ${i + 1}/${retries} failed for ${endpoint}:`, errorMessage);

      if (i === retries - 1) {
        console.error(`Final attempt failed for ${endpoint}:`, error);
        throw error;
      }

      // Wait before retrying (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
    }
  }
};

// Repository data fetching
export const fetchRepositoryData = async (repoName: string): Promise<GitHubRepo> => {
  const cacheKey = `repo_${repoName}`
  const cached = githubCache.get<GitHubRepo>(cacheKey)
  if (cached) return cached

  const data = await githubFetch(`/repos/${GITHUB_ORG}/${repoName}`)
  githubCache.set(cacheKey, data)
  return data
}

export const fetchRepositoryIssues = async (repoName: string): Promise<GitHubIssue[]> => {
  const cacheKey = `issues_${repoName}`
  const cached = githubCache.get<GitHubIssue[]>(cacheKey)
  if (cached) return cached

  const data = await githubFetch(`/repos/${GITHUB_ORG}/${repoName}/issues?state=all&per_page=100`)
  githubCache.set(cacheKey, data)
  return data
}

export const fetchRepositoryPullRequests = async (repoName: string): Promise<GitHubPullRequest[]> => {
  const cacheKey = `prs_${repoName}`
  const cached = githubCache.get<GitHubPullRequest[]>(cacheKey)
  if (cached) return cached

  const data = await githubFetch(`/repos/${GITHUB_ORG}/${repoName}/pulls?state=all&per_page=100`)
  githubCache.set(cacheKey, data)
  return data
}

export const fetchRepositoryCommits = async (repoName: string): Promise<GitHubCommit[]> => {
  const cacheKey = `commits_${repoName}`
  const cached = githubCache.get<GitHubCommit[]>(cacheKey)
  if (cached) return cached

  const data = await githubFetch(`/repos/${GITHUB_ORG}/${repoName}/commits?per_page=100`)
  githubCache.set(cacheKey, data)
  return data
}

export const fetchRepositoryWorkflows = async (repoName: string): Promise<GitHubWorkflow[]> => {
  const cacheKey = `workflows_${repoName}`
  const cached = githubCache.get<GitHubWorkflow[]>(cacheKey)
  if (cached) return cached

  try {
    const data = await githubFetch(`/repos/${GITHUB_ORG}/${repoName}/actions/workflows`)
    githubCache.set(cacheKey, data.workflows || [])
    return data.workflows || []
  } catch (error) {
    console.warn(`Failed to fetch workflows for ${repoName}:`, error)
    return []
  }
}

export const fetchRepositoryWorkflowRuns = async (repoName: string): Promise<GitHubWorkflowRun[]> => {
  const cacheKey = `workflow_runs_${repoName}`
  const cached = githubCache.get<GitHubWorkflowRun[]>(cacheKey)
  if (cached) return cached

  try {
    const data = await githubFetch(`/repos/${GITHUB_ORG}/${repoName}/actions/runs?per_page=50`)
    githubCache.set(cacheKey, data.workflow_runs || [])
    return data.workflow_runs || []
  } catch (error) {
    console.warn(`Failed to fetch workflow runs for ${repoName}:`, error)
    return []
  }
}

export const fetchRepositoryContributors = async (repoName: string): Promise<GitHubMember[]> => {
  const cacheKey = `contributors_${repoName}`
  const cached = githubCache.get<GitHubMember[]>(cacheKey)
  if (cached) return cached

  try {
    const data = await githubFetch(`/repos/${GITHUB_ORG}/${repoName}/contributors?per_page=100`)
    githubCache.set(cacheKey, data)
    return data
  } catch (error) {
    console.warn(`Failed to fetch contributors for ${repoName}:`, error)
    return []
  }
}

export const fetchRepositoryLanguages = async (repoName: string): Promise<Record<string, number>> => {
  const cacheKey = `languages_${repoName}`
  const cached = githubCache.get<Record<string, number>>(cacheKey)
  if (cached) return cached

  try {
    const data = await githubFetch(`/repos/${GITHUB_ORG}/${repoName}/languages`)
    githubCache.set(cacheKey, data)
    return data
  } catch (error) {
    console.warn(`Failed to fetch languages for ${repoName}:`, error)
    return {}
  }
}

export const fetchRepositoryCodeFrequency = async (repoName: string): Promise<Array<[number, number, number]>> => {
  const cacheKey = `code_frequency_${repoName}`
  const cached = githubCache.get<Array<[number, number, number]>>(cacheKey)
  if (cached) return cached

  try {
    const data = await githubFetch(`/repos/${GITHUB_ORG}/${repoName}/stats/code_frequency`)
    // GitHub stats API can return null while processing, so we need to handle this
    const validData = Array.isArray(data) ? data : []
    githubCache.set(cacheKey, validData)
    return validData
  } catch (error) {
    console.warn(`Failed to fetch code frequency for ${repoName}:`, error)
    return []
  }
}

export const fetchTeamMembers = async (): Promise<GitHubMember[]> => {
  const cached = githubCache.get<GitHubMember[]>(CACHE_KEYS.TEAM_MEMBERS)
  if (cached) return cached

  try {
    const data = await githubFetch(`/orgs/${GITHUB_ORG}/members`)
    githubCache.set(CACHE_KEYS.TEAM_MEMBERS, data)
    return data
  } catch (error) {
    console.error("Failed to fetch team members:", error)
    return []
  }
}

// Enhanced team member data with roles and activity
export interface EnhancedTeamMember extends GitHubMember {
  role: "developer" | "reviewer" | "pm" | "supervisor"
  commits: number
  reviews: number
  velocity: number
  projects: string[]
  recentActivity: Array<{
    type: "commit" | "review" | "issue"
    project: string
    date: string
    description: string
  }>
}

// Role definitions based on your team structure
const TEAM_ROLES = {
  developers: [
    "Athooh01",
    "moseeeh",
    "muhembele",
    "Raymond-Madara",
    "rodneyoti",
    "sheilla2708",
    "StellaOiro",
    "Togondola1",
    "Wendy-Akinyi",
  ],
  reviewers: ["avishkarmisra", "wenjie-tehan", "ruth-mierowsky-berrijam"],
  pm: ["vinodhiambo"],
  supervisor: ["paul-oguda"],
}

export const fetchEnhancedTeamData = async (): Promise<{
  developers: EnhancedTeamMember[]
  reviewers: EnhancedTeamMember[]
  pm: EnhancedTeamMember[]
  supervisor: EnhancedTeamMember[]
}> => {
  const cacheKey = "enhanced_team_data"
  const cached = githubCache.get<any>(cacheKey)
  if (cached) return cached

  try {
    // Get all team members
    const allMembers = await fetchTeamMembers()
    const projectsData = await fetchAllProjectsData()

    // Calculate activity for each member
    const enhancedMembers = await Promise.all(
      allMembers.map(async (member) => {
        let commits = 0
        let reviews = 0
        const projects: string[] = []
        const recentActivity: any[] = []

        // Calculate stats from all projects
        projectsData.forEach((project) => {
          // Count commits
          const memberCommits = project.commits.filter((commit) => commit.author?.login === member.login).length
          commits += memberCommits

          // Count reviews (PRs where they are requested reviewers)
          const memberReviews = project.pullRequests.filter((pr) =>
            pr.requested_reviewers.some((reviewer) => reviewer.login === member.login),
          ).length
          reviews += memberReviews

          // Track projects they're active in
          if (memberCommits > 0 || memberReviews > 0) {
            projects.push(project.name)
          }

          // Recent activity
          project.commits
            .filter((commit) => commit.author?.login === member.login)
            .slice(0, 3)
            .forEach((commit) => {
              recentActivity.push({
                type: "commit",
                project: project.name,
                date: commit.commit.author.date,
                description: commit.commit.message.split("\n")[0],
              })
            })
        })

        // Calculate velocity based on recent activity
        const velocity = Math.min(100, Math.max(0, (commits * 10 + reviews * 15) / Math.max(1, projects.length)))

        // Determine role
        let role: "developer" | "reviewer" | "pm" | "supervisor" = "developer"
        if (TEAM_ROLES.reviewers.includes(member.login)) role = "reviewer"
        else if (TEAM_ROLES.pm.includes(member.login)) role = "pm"
        else if (TEAM_ROLES.supervisor.includes(member.login)) role = "supervisor"

        return {
          ...member,
          role,
          commits,
          reviews,
          velocity,
          projects: [...new Set(projects)],
          recentActivity: recentActivity
            .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
            .slice(0, 5),
        }
      }),
    )

    // Group by roles
    const result = {
      developers: enhancedMembers.filter((m) => m.role === "developer"),
      reviewers: enhancedMembers.filter((m) => m.role === "reviewer"),
      pm: enhancedMembers.filter((m) => m.role === "pm"),
      supervisor: enhancedMembers.filter((m) => m.role === "supervisor"),
    }

    githubCache.set(cacheKey, result, 10 * 60 * 1000) // Cache for 10 minutes
    return result
  } catch (error) {
    console.error("Error fetching enhanced team data:", error)
    return {
      developers: [],
      reviewers: [],
      pm: [],
      supervisor: [],
    }
  }
}

// Get all milestones across projects
export const fetchAllMilestones = async () => {
  const cacheKey = "all_milestones"
  const cached = githubCache.get<any>(cacheKey)
  if (cached) return cached

  try {
    const projectsData = await fetchAllProjectsData()

    const allMilestones = new Map()
    const milestoneStats = {
      total: 0,
      open: 0,
      closed: 0,
      overdue: 0,
    }

    // Collect all unique milestones across projects
    projectsData.forEach((project) => {
      project.issues.forEach((issue) => {
        if (issue.milestone) {
          const key = `${project.name}-${issue.milestone.id}`
          if (!allMilestones.has(key)) {
            const isOverdue =
              issue.milestone.due_on &&
              new Date(issue.milestone.due_on) < new Date() &&
              issue.milestone.state === "open"

            allMilestones.set(key, {
              ...issue.milestone,
              project: project.name,
              isOverdue,
              issues: [],
            })

            milestoneStats.total++
            if (issue.milestone.state === "open") {
              milestoneStats.open++
              if (isOverdue) milestoneStats.overdue++
            } else {
              milestoneStats.closed++
            }
          }
          // Add issue to milestone's issues array
          allMilestones.get(key).issues.push(issue)
        }
      })
    })

    // Convert milestones to array and sort by due date
    const milestonesArray = Array.from(allMilestones.values()).sort((a, b) => {
      if (!a.due_on) return 1
      if (!b.due_on) return -1
      return new Date(a.due_on).getTime() - new Date(b.due_on).getTime()
    })

    const result = {
      milestones: milestonesArray,
      stats: milestoneStats,
      projectsData,
    }

    githubCache.set(cacheKey, result, 10 * 60 * 1000)
    return result
  } catch (error) {
    console.error("Error fetching milestones:", error)
    return {
      milestones: [],
      stats: { total: 0, open: 0, closed: 0, overdue: 0 },
      projectsData: [],
    }
  }
}

// Analytics and calculations
export const calculateProjectProgress = (issues: GitHubIssue[]): number => {
  const totalIssues = issues.filter((issue) => !issue.pull_request).length
  const closedIssues = issues.filter((issue) => !issue.pull_request && issue.state === "closed").length
  return totalIssues > 0 ? Math.round((closedIssues / totalIssues) * 100) : 0
}

export const determineProjectStatus = (
  progress: number,
  openIssues: number,
  overdueMilestones: number,
): "on-track" | "at-risk" | "behind" => {
  if (overdueMilestones > 0) return "behind"
  if (progress >= 70 && openIssues < 15) return "on-track"
  if (progress >= 50 && openIssues < 25) return "at-risk"
  return "behind"
}

export const extractMilestones = (issues: GitHubIssue[]): GitHubIssue["milestone"][] => {
  const milestones = new Map<number, GitHubIssue["milestone"]>()

  issues.forEach((issue) => {
    if (issue.milestone) {
      milestones.set(issue.milestone.id, issue.milestone)
    }
  })

  return Array.from(milestones.values()).filter(Boolean)
}

export const calculateAnalytics = async (
  repoName: string,
  commits: GitHubCommit[],
): Promise<ProjectData["analytics"]> => {
  try {
    const [languages, codeFrequency] = await Promise.all([
      fetchRepositoryLanguages(repoName),
      fetchRepositoryCodeFrequency(repoName),
    ])

    // Process code frequency data with proper validation
    const processedCodeFrequency = Array.isArray(codeFrequency)
      ? codeFrequency
          .filter((item) => Array.isArray(item) && item.length === 3)
          .map(([timestamp, additions, deletions]) => ({
            week: new Date(timestamp * 1000).toISOString().split("T")[0],
            additions: Math.abs(additions || 0),
            deletions: Math.abs(deletions || 0),
          }))
      : []

    // Calculate contributor stats with better error handling
    const contributorStats = commits.reduce(
      (acc, commit) => {
        try {
          const author = commit.author?.login || commit.commit?.author?.name || "Unknown"
          if (!acc[author]) {
            acc[author] = { author, commits: 0, additions: 0, deletions: 0 }
          }
          acc[author].commits++
          if (commit.stats) {
            acc[author].additions += commit.stats.additions || 0
            acc[author].deletions += commit.stats.deletions || 0
          }
        } catch (error) {
          console.warn("Error processing commit for contributor stats:", error)
        }
        return acc
      },
      {} as Record<string, { author: string; commits: number; additions: number; deletions: number }>,
    )

    return {
      totalCommits: commits.length,
      totalIssues: 0, // Will be set by caller
      totalPRs: 0, // Will be set by caller
      codeFrequency: processedCodeFrequency,
      languageStats: languages || {},
      contributorStats: Object.values(contributorStats),
    }
  } catch (error) {
    console.error(`Error calculating analytics for ${repoName}:`, error)
    return {
      totalCommits: commits.length,
      totalIssues: 0,
      totalPRs: 0,
      codeFrequency: [],
      languageStats: {},
      contributorStats: [],
    }
  }
}

// Main function to fetch all project data
export const fetchAllProjectsData = async (): Promise<ProjectData[]> => {
  const cached = githubCache.get<ProjectData[]>(CACHE_KEYS.ALL_PROJECTS)
  if (cached) return cached

  const projectsData: ProjectData[] = []

  for (const [projectName, config] of Object.entries(PROJECTS)) {
    try {
      console.log(`Fetching comprehensive data for ${projectName}...`)

      const [repo, issues, pullRequests, commits, workflows, workflowRuns, contributors] = await Promise.all([
        fetchRepositoryData(config.repo),
        fetchRepositoryIssues(config.repo),
        fetchRepositoryPullRequests(config.repo),
        fetchRepositoryCommits(config.repo),
        fetchRepositoryWorkflows(config.repo),
        fetchRepositoryWorkflowRuns(config.repo),
        fetchRepositoryContributors(config.repo),
      ])

      // Filter out PRs from issues (GitHub API includes PRs in issues endpoint)
      const actualIssues = issues.filter((issue) => !issue.pull_request)

      const progress = calculateProjectProgress(actualIssues)
      const openIssues = actualIssues.filter((issue) => issue.state === "open")
      const milestones = extractMilestones(actualIssues)
      const overdueMilestones = milestones.filter(
        (m) => m && m.due_on && new Date(m.due_on) < new Date() && m.state === "open",
      ).length

      const status = determineProjectStatus(progress, openIssues.length, overdueMilestones)
      const analytics = await calculateAnalytics(config.repo, commits)

      // Update analytics with actual counts
      analytics.totalIssues = actualIssues.length
      analytics.totalPRs = pullRequests.length

      projectsData.push({
        name: projectName,
        repo,
        issues: actualIssues,
        pullRequests,
        commits,
        workflows,
        workflowRuns,
        contributors,
        progress,
        status,
        milestones,
        analytics,
      })

      console.log(`Successfully fetched comprehensive data for ${projectName}`)
    } catch (error) {
      console.error(`Error fetching data for ${projectName}:`, error)

      // Add fallback data for failed projects
      projectsData.push({
        name: projectName,
        repo: {
          id: 0,
          name: config.repo,
          full_name: `${GITHUB_ORG}/${config.repo}`,
          description: `${projectName} project repository`,
          language: "TypeScript",
          stargazers_count: 0,
          forks_count: 0,
          open_issues_count: 0,
          updated_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          pushed_at: new Date().toISOString(),
          default_branch: "main",
          size: 0,
          topics: [],
          visibility: "private",
          archived: false,
          disabled: false,
        } as GitHubRepo,
        issues: [],
        pullRequests: [],
        commits: [],
        workflows: [],
        workflowRuns: [],
        contributors: [],
        progress: 0,
        status: "behind",
        milestones: [],
        analytics: {
          totalCommits: 0,
          totalIssues: 0,
          totalPRs: 0,
          codeFrequency: [],
          languageStats: {},
          contributorStats: [],
        },
      })
    }
  }

  // Cache the results for 10 minutes
  githubCache.set(CACHE_KEYS.ALL_PROJECTS, projectsData, 10 * 60 * 1000)
  return projectsData
}

type MilestoneIssue = {
  milestoneNumber: number;
  state: "open" | "closed";
};

/**
 * Calculate overall milestone progress based on 6 planned milestones
 * Each milestone has equal weight (1/6) in the overall progress
 */
function calculateMilestoneProgress(allIssues: MilestoneIssue[]): number {
  const TOTAL_PLANNED_MILESTONES = 6;

  // Group issues by milestone number
  const issuesByMilestone = new Map<number, MilestoneIssue[]>();

  for (const issue of allIssues) {
    if (!issuesByMilestone.has(issue.milestoneNumber)) {
      issuesByMilestone.set(issue.milestoneNumber, []);
    }
    issuesByMilestone.get(issue.milestoneNumber)!.push(issue);
  }

  let totalProgress = 0;

  // Calculate progress for each of the 6 planned milestones
  for (let milestoneNum = 1; milestoneNum <= TOTAL_PLANNED_MILESTONES; milestoneNum++) {
    const milestoneIssues = issuesByMilestone.get(milestoneNum) || [];

    if (milestoneIssues.length === 0) {
      // Milestone doesn't exist or has no issues - 0% progress
      totalProgress += 0;
    } else {
      // Calculate milestone completion
      const totalIssues = milestoneIssues.length;
      const closedIssues = milestoneIssues.filter(issue => issue.state === "closed").length;
      const milestoneCompletion = (closedIssues / totalIssues) * 100;
      totalProgress += milestoneCompletion;
    }
  }

  // Each milestone contributes equally (1/6) to overall progress
  const overallProgress = totalProgress / TOTAL_PLANNED_MILESTONES;

  return Math.round(overallProgress);
}

/**
 * Extract milestone number from milestone title
 * Supports patterns like "Milestone 1", "Milestone 2", etc.
 */
function extractMilestoneNumber(milestoneTitle: string): number | null {
  const match = milestoneTitle.match(/Milestone\s+(\d+)/i);
  return match ? parseInt(match[1], 10) : null;
}

/**
 * Transform GitHub issues to the format expected by calculateMilestoneProgress
 */
function transformGitHubIssuesToMilestoneIssues(projectsData: ProjectData[]): MilestoneIssue[] {
  const milestoneIssues: MilestoneIssue[] = [];

  for (const project of projectsData) {
    for (const issue of project.issues) {
      if (issue.milestone && issue.milestone.title) {
        const milestoneNumber = extractMilestoneNumber(issue.milestone.title);
        if (milestoneNumber !== null) {
          milestoneIssues.push({
            milestoneNumber,
            state: issue.state
          });
        }
      }
    }
  }

  return milestoneIssues;
}

interface IdealProgressConfig {
  strategy: 'time-based' | 'linear' | 'milestone-dates';
  projectStartDate?: string; // ISO date string
  projectEndDate?: string;   // ISO date string
  currentMilestone?: number; // Which milestone should be completed by now
}

/**
 * Calculate ideal milestone progress based on different strategies
 */
function calculateIdealMilestoneProgress(
  projectsData: ProjectData[],
  config: IdealProgressConfig = { strategy: 'linear' }
): number {
  const TOTAL_PLANNED_MILESTONES = 6;

  switch (config.strategy) {
    case 'time-based':
      if (!config.projectStartDate || !config.projectEndDate) {
        throw new Error('Time-based strategy requires projectStartDate and projectEndDate');
      }
      return calculateTimeBasedIdeal(config.projectStartDate, config.projectEndDate);

    case 'milestone-dates':
      return calculateMilestoneDateBasedIdeal(projectsData);

    case 'linear':
    default:
      // Default: Assume we should be at Milestone 2 completion (33.33%)
      return calculateLinearIdeal(config.currentMilestone || 2);
  }
}

function calculateTimeBasedIdeal(projectStartDate: string, projectEndDate: string): number {
  const start = new Date(projectStartDate);
  const end = new Date(projectEndDate);
  const now = new Date();

  const totalDuration = end.getTime() - start.getTime();
  const elapsedDuration = now.getTime() - start.getTime();

  if (elapsedDuration <= 0) return 0;
  if (elapsedDuration >= totalDuration) return 100;

  const timeProgress = (elapsedDuration / totalDuration) * 100;
  return Math.round(Math.min(100, Math.max(0, timeProgress)));
}

function calculateMilestoneDateBasedIdeal(projectsData: ProjectData[]): number {
  // Get all milestones with due dates
  const milestonesWithDates: Array<{ milestoneNumber: number; dueDate: Date }> = [];

  projectsData.forEach(project => {
    project.issues.forEach(issue => {
      if (issue.milestone && issue.milestone.due_on && issue.milestone.title) {
        const milestoneNumber = extractMilestoneNumber(issue.milestone.title);
        if (milestoneNumber !== null) {
          milestonesWithDates.push({
            milestoneNumber,
            dueDate: new Date(issue.milestone.due_on)
          });
        }
      }
    });
  });

  if (milestonesWithDates.length === 0) {
    return calculateLinearIdeal(2); // Fallback
  }

  // Sort by milestone number and get unique milestones
  const uniqueMilestones = Array.from(
    new Map(milestonesWithDates.map(m => [m.milestoneNumber, m])).values()
  ).sort((a, b) => a.milestoneNumber - b.milestoneNumber);

  const now = new Date();
  let idealProgress = 0;

  uniqueMilestones.forEach(milestone => {
    if (now >= milestone.dueDate) {
      // This milestone should be 100% complete
      idealProgress += (100 / 6); // Each milestone is worth 16.67%
    } else {
      // This milestone should be partially complete based on how close we are to due date
      const previousMilestone = uniqueMilestones.find(m => m.milestoneNumber === milestone.milestoneNumber - 1);
      if (previousMilestone && now >= previousMilestone.dueDate) {
        // We're between previous milestone due date and current milestone due date
        const totalDuration = milestone.dueDate.getTime() - previousMilestone.dueDate.getTime();
        const elapsed = now.getTime() - previousMilestone.dueDate.getTime();
        const partialProgress = Math.min(1, Math.max(0, elapsed / totalDuration));
        idealProgress += partialProgress * (100 / 6);
      }
    }
  });

  return Math.round(idealProgress);
}

function calculateLinearIdeal(currentExpectedMilestone: number): number {
  // Simple linear calculation: each milestone represents 16.67% (100/6)
  const progressPerMilestone = 100 / 6;
  return Math.round(currentExpectedMilestone * progressPerMilestone);
}

export const fetchDashboardMetrics = async () => {
  try {
    const projectsData = await fetchAllProjectsData()

    const totalIssues = projectsData.reduce((sum, project) => sum + project.issues.length, 0)
    const totalClosedIssues = projectsData.reduce(
      (sum, project) => sum + project.issues.filter((issue) => issue.state === "closed").length,
      0,
    )

    const totalPRs = projectsData.reduce((sum, project) => sum + project.pullRequests.length, 0)
    const totalMergedPRs = projectsData.reduce(
      (sum, project) => sum + project.pullRequests.filter((pr) => pr.merged_at !== null).length,
      0,
    )

    const avgProgress =
      projectsData.length > 0
        ? projectsData.reduce((sum, project) => sum + project.progress, 0) / projectsData.length
        : 0

    // MILESTONE PROGRESS CALCULATION
    const milestoneIssues = transformGitHubIssuesToMilestoneIssues(projectsData);
    const milestoneProgress = calculateMilestoneProgress(milestoneIssues);

    // IDEAL MILESTONE PROGRESS CALCULATION
    const idealMilestoneProgress = calculateIdealMilestoneProgress(projectsData, {
      strategy: 'linear',
      currentMilestone: 2 // Adjust this based on your project timeline
    });

    // Team Velocity: Issues completed per sprint (last 2 sprints)
    const twoWeeksAgo = new Date(Date.now() - 14 * 24 * 60 * 60 * 1000)
    const fourWeeksAgo = new Date(Date.now() - 28 * 24 * 60 * 60 * 1000)

    const currentSprintIssues = projectsData.reduce((sum, project) =>
      sum + project.issues.filter((issue) =>
        issue.state === "closed" &&
        issue.closed_at &&
        new Date(issue.closed_at) >= twoWeeksAgo
      ).length, 0
    )

    const lastSprintIssues = projectsData.reduce((sum, project) =>
      sum + project.issues.filter((issue) =>
        issue.state === "closed" &&
        issue.closed_at &&
        new Date(issue.closed_at) >= fourWeeksAgo &&
        new Date(issue.closed_at) < twoWeeksAgo
      ).length, 0
    )

    const teamVelocity = Math.round((currentSprintIssues + lastSprintIssues) / 2)

    // Issue Resolution: Daily throughput (issues closed today vs sprint average)
    // Issue Resolution: 7-day resolution rate (more stable metric)
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)

    // Issues opened in last 7 days
    const recentIssuesOpened = projectsData.reduce((sum, project) =>
      sum + project.issues.filter((issue) =>
        new Date(issue.created_at) >= sevenDaysAgo
      ).length, 0
    )

    // Issues closed in last 7 days
    const recentIssuesClosed = projectsData.reduce((sum, project) =>
      sum + project.issues.filter((issue) =>
        issue.state === "closed" &&
        issue.closed_at &&
        new Date(issue.closed_at) >= sevenDaysAgo
      ).length, 0
    )

    // Resolution rate: % of opened issues that got resolved
    const issueResolution = totalIssues > 0 ? Math.round((totalClosedIssues / totalIssues) * 100) : 0

    const totalCommits = projectsData.reduce((sum, project) => sum + project.commits.length, 0)

    return {
      portfolioHealth: Math.round(avgProgress),
      milestoneProgress,
      idealMilestoneProgress,
      teamVelocity,
      issueResolution,
      projectsData,
      totalCommits,
      totalIssues,
      totalPRs,
      totalMergedPRs,
      totalClosedIssues,
    }
  } catch (error) {
    console.error("Error fetching dashboard metrics:", error)
    return {
      portfolioHealth: 0,
      milestoneProgress: 0,
      idealMilestoneProgress: 0,
      teamVelocity: 0,
      issueResolution: 0,
      projectsData: [],
      totalCommits: 0,
      totalIssues: 0,
      totalPRs: 0,
      totalMergedPRs: 0,
      totalClosedIssues: 0,
    }
  }
}
