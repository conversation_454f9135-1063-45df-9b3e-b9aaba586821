// Simple in-memory cache with TTL
interface CacheItem<T> {
  data: T
  expiry: number
}

class GitHubCache {
  private cache = new Map<string, CacheItem<any>>()

  get<T>(key: string): T | null {
    const item = this.cache.get(key)
    if (!item) return null

    if (Date.now() > item.expiry) {
      this.cache.delete(key)
      return null
    }

    return item.data
  }

  set<T>(key: string, data: T, ttlMs: number = 5 * 60 * 1000): void {
    this.cache.set(key, {
      data,
      expiry: Date.now() + ttlMs,
    })
  }

  clear(): void {
    this.cache.clear()
  }

  delete(key: string): void {
    this.cache.delete(key)
  }
}

export const githubCache = new GitHubCache()

export const CACHE_KEYS = {
  ALL_PROJECTS: "all_projects",
  TEAM_MEMBERS: "team_members",
  ANALYTICS: "analytics",
  MILESTONES: "milestones",
} as const
