// Move GitHub API logic to server-side only
// This file will now contain types and utilities only

export interface GitHubRepo {
  name: string
  description: string
  language: string
  stargazers_count: number
  forks_count: number
  open_issues_count: number
  updated_at: string
  default_branch: string
}

export interface GitHubIssue {
  id: number
  number: number
  title: string
  state: string
  created_at: string
  updated_at: string
  assignee?: {
    login: string
    avatar_url: string
  }
  labels: Array<{
    name: string
    color: string
  }>
}

export interface GitHubPullRequest {
  id: number
  number: number
  title: string
  state: string
  created_at: string
  updated_at: string
  user: {
    login: string
    avatar_url: string
  }
  assignee?: {
    login: string
    avatar_url: string
  }
  additions: number
  deletions: number
}

export interface GitHubCommit {
  sha: string
  commit: {
    message: string
    author: {
      name: string
      date: string
    }
  }
  author?: {
    login: string
    avatar_url: string
  }
}

export interface ProjectData {
  name: string
  repo: GitHubRepo
  issues: GitHubIssue[]
  pullRequests: GitHubPullRequest[]
  commits: GitHubCommit[]
  progress: number
  status: "on-track" | "at-risk" | "behind"
}

export const PROJECTS = {
  strawberri: { projectId: 5, repo: "strawberri" },
  gojiberri: { projectId: 4, repo: "gojiberri" },
  mulberri: { projectId: 6, repo: "mulberri" },
  cranberri: { projectId: 8, repo: "cranberri" },
  elderberri: { projectId: 9, repo: "elderberri" },
}

export const calculateProjectProgress = (issues: GitHubIssue[]): number => {
  const totalIssues = issues.length
  const closedIssues = issues.filter((issue) => issue.state === "closed").length
  return totalIssues > 0 ? Math.round((closedIssues / totalIssues) * 100) : 0
}

export const determineProjectStatus = (progress: number, issueCount: number): "on-track" | "at-risk" | "behind" => {
  if (progress >= 70 && issueCount < 15) return "on-track"
  if (progress >= 50 && issueCount < 25) return "at-risk"
  return "behind"
}
