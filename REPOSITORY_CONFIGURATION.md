# Repository Configuration Guide

This guide explains how to configure the PM Dashboard to fetch data from your GitHub repositories.

## Environment Variables

Create a `.env.local` file in your project root with the following variables:

```bash
# Required: Your GitHub Personal Access Token
GITHUB_TOKEN=your_github_token_here

# Required: Your GitHub organization or username
GITHUB_ORG=your-organization-name
```

### Getting a GitHub Token

1. Go to GitHub Settings → Developer settings → Personal access tokens
2. Generate a new token with the following permissions:
   - `repo` (Full control of private repositories)
   - `read:org` (Read org and team membership)
   - `read:user` (Read user profile data)

## Repository Configuration

### Option 1: Using Environment Variables (Recommended)

Set the `GITHUB_ORG` environment variable to your organization name, and the system will automatically fetch from your repositories defined in the `PROJECTS` object.

### Option 2: Direct Code Modification

Edit `lib/github-api-enhanced.ts`:

1. **Change the organization:**
   ```typescript
   const GITHUB_ORG = process.env.GITHUB_ORG || "your-organization-name"
   ```

2. **Update the projects list:**
   ```typescript
   export const PROJECTS = {
     project1: { projectId: 1, repo: "your-repo-1" },
     project2: { projectId: 2, repo: "your-repo-2" },
     project3: { projectId: 3, repo: "your-repo-3" },
     // Add more projects as needed
   }
   ```

3. **Update team roles:**
   ```typescript
   const TEAM_ROLES = {
     developers: [
       "developer1-github-username",
       "developer2-github-username",
     ],
     reviewers: [
       "reviewer1-github-username",
       "reviewer2-github-username",
     ],
     pm: [
       "pm1-github-username",
     ],
     supervisor: [
       "supervisor1-github-username",
     ],
   }
   ```

## Project Structure

Each project in the `PROJECTS` object should have:
- `projectId`: A unique identifier for the project
- `repo`: The repository name (without the organization prefix)

The system will automatically construct the full repository path as `{GITHUB_ORG}/{repo}`.

## Data Fetched

The dashboard fetches the following data for each repository:
- Repository information (stars, forks, language, etc.)
- Issues and their states
- Pull requests and their states
- Commits and commit history
- GitHub Actions workflows and runs
- Contributors and their activity
- Milestones and their progress
- Code frequency and language statistics

## Team Roles

Team members are categorized into roles based on the `TEAM_ROLES` configuration:
- **Developers**: Contributors who primarily write code
- **Reviewers**: Team members who review pull requests
- **PM**: Project managers who oversee project progress
- **Supervisor**: Senior team members who supervise the project

## Caching

The system uses intelligent caching to avoid hitting GitHub API rate limits:
- Repository data: Cached for 10 minutes
- Team data: Cached for 10 minutes
- Analytics data: Cached for 10 minutes

## Troubleshooting

1. **"GitHub token not configured" error**: Make sure your `.env.local` file contains a valid `GITHUB_TOKEN`
2. **"Failed to fetch data" error**: Check that your GitHub token has the required permissions
3. **Empty data**: Verify that the repository names in `PROJECTS` match your actual repositories
4. **Team members not showing**: Ensure the GitHub usernames in `TEAM_ROLES` are correct

## Example Configuration

Here's a complete example configuration:

**.env.local:**
```bash
GITHUB_TOKEN=ghp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
GITHUB_ORG=mycompany
```

**lib/github-api-enhanced.ts:**
```typescript
export const PROJECTS = {
  frontend: { projectId: 1, repo: "frontend-app" },
  backend: { projectId: 2, repo: "backend-api" },
  mobile: { projectId: 3, repo: "mobile-app" },
}

const TEAM_ROLES = {
  developers: ["john-doe", "jane-smith", "bob-wilson"],
  reviewers: ["senior-dev1", "senior-dev2"],
  pm: ["project-manager"],
  supervisor: ["tech-lead"],
}
```

This configuration will fetch data from:
- `mycompany/frontend-app`
- `mycompany/backend-api`
- `mycompany/mobile-app`
